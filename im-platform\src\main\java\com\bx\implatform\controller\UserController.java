package com.bx.implatform.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.bx.implatform.dto.RegisterDTO;
import com.bx.implatform.dto.ResetPasswordDTO;
import com.bx.implatform.dto.UserDeptDTO;
import com.bx.implatform.dto.UserQueryDTO;
import com.bx.implatform.entity.User;
import com.bx.implatform.mapper.UserDeptMapper;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.IUserService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.session.UserSession;
import com.bx.implatform.util.BeanUtils;
import com.bx.implatform.vo.OnlineTerminalVO;
import com.bx.implatform.vo.UserDeptVO;
import com.bx.implatform.vo.UserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "用户")
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final IUserService userService;

    private final UserDeptMapper userDeptMapper;

    @GetMapping("/terminal/online")
    @ApiOperation(value = "判断用户哪个终端在线", notes = "返回在线的用户id的终端集合")
    public Result<List<OnlineTerminalVO>> getOnlineTerminal(@NotEmpty @RequestParam("userIds") String userIds) {
        return ResultUtils.success(userService.getOnlineTerminals(userIds));
    }


    @GetMapping("/self")
    @ApiOperation(value = "获取当前用户信息", notes = "获取当前用户信息")
    public Result<UserVO> findSelfInfo() {
        UserSession session = SessionContext.getSession();
        User user = userService.getById(session.getUserId());
        UserVO userVO = BeanUtils.copyProperties(user, UserVO.class);
        UserDeptDTO userDeptDTO = new UserDeptDTO();
        userDeptDTO.setUserNameList(Arrays.asList(user.getUserName()));
        List<UserDeptVO> userDepts = userDeptMapper.findAllDeptAndUser(userDeptDTO);
        Map<String, List<UserDeptVO>> listMap = userDepts.stream().collect(Collectors.groupingBy(UserDeptVO::getUserName, Collectors.toList()));
        if (StrUtil.isNotBlank(userVO.getChatObjectIds())) {
            userVO.setConnectionPersons(JSONObject.parseArray(userVO.getChatObjectIds(), UserDeptVO.class));
        }
        List<UserDeptVO> userDeptVOS = listMap.get(userVO.getUserName());
        if (CollUtil.isNotEmpty(userDeptVOS)) {
            userVO.setUserDepts(userDeptVOS);
        }
        return ResultUtils.success(userVO);
    }


    @GetMapping("/find/{id}")
    @ApiOperation(value = "查找用户", notes = "根据id查找用户")
    public Result<UserVO> findById(@NotEmpty @PathVariable("id") Long id) {
        return ResultUtils.success(userService.findUserById(id));
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改用户信息", notes = "修改用户信息，仅允许修改登录用户信息")
    public Result update(@Valid @RequestBody UserVO vo) {
        userService.update(vo);
        return ResultUtils.success();
    }

    @GetMapping("/findByUser")
    @ApiOperation(value = "查找用户", notes = "根据用户名或昵称查找用户")
    public Result<List<UserVO>> findByName(@RequestParam("name") String name) {
        return ResultUtils.success(userService.findUserByName(name));
    }

    @PostMapping("/add")
    @ApiOperation(value = "新增用户", notes = "用户管理")
    public Result add(@Valid @RequestBody RegisterDTO dto) {
        userService.register(dto);
        return ResultUtils.success();
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除用户", notes = "用户管理")
    public Result delete(@PathVariable("id") Long id) {
        userService.deleteById(id);
        return ResultUtils.success();
    }

    @PostMapping("/getUserListPage")
    @ApiOperation(value = "分页查询用户", notes = "用户管理")
    public Result getUserListPage(@RequestBody UserQueryDTO userQueryDTO) {
        Result result = userService.getUserListPage(userQueryDTO);
        return result;
    }

    /**
     * 获取临时用户可以聊天的用户列表
     */
    @GetMapping("/getTempUserChatList")
    @ApiOperation(value = "获取临时用户可以聊天的用户列表", notes = "获取临时用户可以聊天的用户列表")
    public Result<List<UserVO>> getTempUserChatList(@RequestParam("userName") String userName) {
        return userService.getTempUserChatList(userName);
    }

    @PutMapping("/resetPassword")
    @ApiOperation(value = "重置密码", notes = "重置密码")
    public Result resetPassword(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO) {
        userService.resetPassword(resetPasswordDTO);
        return ResultUtils.success();
    }
}

