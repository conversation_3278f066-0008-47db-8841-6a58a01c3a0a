package com.bx.implatform.dto;

import com.bx.implatform.vo.UserDeptVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("用户注册DTO")
public class RegisterDTO {

    @Length(max = 64, message = "用户名不能大于64字符")
    @NotEmpty(message = "用户名不可为空")
    @ApiModelProperty(value = "用户名")
    private String userName;

    @Length(min = 5, max = 20, message = "密码长度必须在5-20个字符之间")
    @NotEmpty(message = "用户密码不可为空")
    @ApiModelProperty(value = "用户密码")
    private String password;

    @Length(max = 64, message = "昵称不能大于64字符")
    @NotEmpty(message = "用户昵称不可为空")
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 过期时间，天数，针对临时用户
     */
    @ApiModelProperty(value = "过期时间，天数，针对临时用户")
    private Long expireDays;

    /**
     * 用户角色，固定三种角色：管理员、普通用户、临时用户
     */
    @NotBlank(message = "用户角色不可为空")
    @ApiModelProperty(value = "用户角色，固定三种角色：管理员、普通用户、临时用户")
    private String role = "普通用户";

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 部门信息
     */
    @ApiModelProperty(value = "部门信息")
    List<UserDeptVO> userDepts;

    /**
     * 临时用户联系人
     */
    @ApiModelProperty(value = "临时用户联系人")
    List<UserDeptVO> connectionPersons;
}
