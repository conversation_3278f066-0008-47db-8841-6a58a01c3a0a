package com.bx.implatform.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bx.imcommon.util.SmCryptoUtil;
import com.bx.implatform.entity.GroupMessage;
import com.bx.implatform.entity.PrivateMessage;
import com.bx.implatform.enums.MessageType;
import com.bx.implatform.service.IGroupMessageService;
import com.bx.implatform.service.IPrivateMessageService;
import com.bx.implatform.service.thirdparty.FileService;
import com.bx.implatform.vo.UploadFileVO;
import com.bx.implatform.vo.UploadImageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2024-05-20 9:27
 */
@Slf4j
@Configuration
@EnableScheduling
public class ScheduleTask {
    @Value("${minio.public}")
    private String minIoServer;
    @Value("${minio.bucketName}")
    private String bucketName;
    @Value("${minio.imagePath}")
    private String imagePath;
    @Value("${minio.filePath}")
    private String filePath;
    @Value("${minio.videoPath}")
    private String videoPath;
    @Autowired
    private FileConfigProperties fileConfigProperties;

    @Autowired
    private IPrivateMessageService privateMessageService;

    @Autowired
    private IGroupMessageService groupMessageService;

    @Autowired
    private FileService fileService;

    //每天1点执行，清理私聊过期图片、文件信息
    @Scheduled(cron = "0 0 1 * * ?")
    //1分钟执行一次
    //@Scheduled(fixedRate = 60 * 1000)
    void cleanPrivateMessageExpiredFileAndImageTasks() {
        if (!fileConfigProperties.isClean()) {
            return;
        }
        int day = fileConfigProperties.getCleanDays();
        if (day <= 0) {
            return;
        }
        try {
            log.info("--------开始执行清理私聊过期图片、文件信息定时任务" + LocalDateTime.now());
            List<PrivateMessage> privateMessages = privateMessageService.list(Wrappers.<PrivateMessage>lambdaQuery().lt(PrivateMessage::getSendTime, DateUtil.offsetDay(new Date(), -day)));
            if (CollUtil.isEmpty(privateMessages)) {
                return;
            }
            for (PrivateMessage privateMessage : privateMessages) {
                if (MessageType.IMAGE.code() == privateMessage.getType()) {
                    String content = SmCryptoUtil.decrypt(privateMessage.getContent());
                    UploadImageVO uploadImageVO = JSONUtil.toBean(content, UploadImageVO.class);
                    //原图
                    String originUrl = uploadImageVO.getOriginUrl();
                    if (StrUtil.isNotBlank(originUrl)) {
                        String fileName = originUrl.substring(originUrl.indexOf("/" + imagePath + "/") + imagePath.length() + 2);
                        fileService.deleteImage(fileName);
                    }
                    //缩略图
                    String thumbUrl = uploadImageVO.getThumbUrl();
                    if (StrUtil.isNotBlank(thumbUrl)) {
                        String fileName = thumbUrl.substring(originUrl.indexOf("/" + imagePath + "/") + imagePath.length() + 2);
                        fileService.deleteImage(fileName);
                    }
                }

                if (MessageType.FILE.code() == privateMessage.getType()) {
                    String content = SmCryptoUtil.decrypt(privateMessage.getContent());
                    UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                    String url = uploadFileVO.getUrl();
                    if (StrUtil.isNotBlank(url)) {
                        String fileName = url.substring(url.lastIndexOf("/" + filePath + "/") + filePath.length() + 2);
                        fileService.deleteFile(fileName);
                    }
                }

                if (MessageType.AUDIO.code() == privateMessage.getType()) {
                    String content = SmCryptoUtil.decrypt(privateMessage.getContent());
                    UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                    String url = uploadFileVO.getUrl();
                    if (StrUtil.isNotBlank(url)) {
                        String fileName = url.substring(url.lastIndexOf("/" + filePath + "/") + filePath.length() + 2);
                        fileService.deleteFile(fileName);
                    }
                }
            }
            privateMessageService.removeByIds(privateMessages.stream().map(PrivateMessage::getId).collect(Collectors.toSet()));
            log.info("--------清理私聊过期图片、文件信息定时任务结束--------");
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("清理私聊过期图片定时任务失败", e);
        }
    }

    //每天1点执行，清理群聊过期图片、文件信息
    @Scheduled(cron = "0 0 1 * * ?")
    //@Scheduled(fixedRate = 60 * 1000)
    void cleanGroupMessageExpiredFileAndImageTasks() {
        if (!fileConfigProperties.isClean()) {
            return;
        }
        int day = fileConfigProperties.getCleanDays();
        if (day <= 0) {
            return;
        }
        try {
            log.info("--------开始执行清理群聊过期图片、文件信息定时任务" + LocalDateTime.now());
            List<GroupMessage> groupMessages = groupMessageService.list(Wrappers.<GroupMessage>lambdaQuery().lt(GroupMessage::getSendTime, DateUtil.offsetDay(new Date(), -day)));
            if (CollUtil.isEmpty(groupMessages)) {
                return;
            }
            for (GroupMessage groupMessage : groupMessages) {
                if (MessageType.IMAGE.code() == groupMessage.getType()) {
                    String content = SmCryptoUtil.decrypt(groupMessage.getContent());
                    UploadImageVO uploadImageVO = JSONUtil.toBean(content, UploadImageVO.class);
                    //原图
                    String originUrl = uploadImageVO.getOriginUrl();
                    if (StrUtil.isNotBlank(originUrl)) {
                        String fileName = originUrl.substring(originUrl.indexOf("/" + imagePath + "/") + imagePath.length() + 2);
                        fileService.deleteImage(fileName);
                    }
                    //缩略图
                    String thumbUrl = uploadImageVO.getThumbUrl();
                    if (StrUtil.isNotBlank(thumbUrl)) {
                        String fileName = thumbUrl.substring(originUrl.indexOf("/" + imagePath + "/") + imagePath.length() + 2);
                        fileService.deleteImage(fileName);
                    }
                }

                if (MessageType.FILE.code() == groupMessage.getType()) {
                    String content = SmCryptoUtil.decrypt(groupMessage.getContent());
                    UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                    String url = uploadFileVO.getUrl();
                    if (StrUtil.isNotBlank(url)) {
                        String fileName = url.substring(url.lastIndexOf("/" + filePath + "/") + filePath.length() + 2);
                        fileService.deleteFile(fileName);
                    }
                }

                if (MessageType.AUDIO.code() == groupMessage.getType()) {
                    String content = SmCryptoUtil.decrypt(groupMessage.getContent());
                    UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                    String url = uploadFileVO.getUrl();
                    if (StrUtil.isNotBlank(url)) {
                        String fileName = url.substring(url.lastIndexOf("/" + filePath + "/") + filePath.length() + 2);
                        fileService.deleteFile(fileName);
                    }
                }
            }
            groupMessageService.removeByIds(groupMessages.stream().map(GroupMessage::getId).collect(Collectors.toSet()));
            log.info("--------清理群聊过期图片、文件信息定时任务结束--------");
        } catch (Throwable e) {
            //保证定时任务不会因为异常退出
            log.error("清理群聊过期图片定时任务失败", e);
        }
    }
}
