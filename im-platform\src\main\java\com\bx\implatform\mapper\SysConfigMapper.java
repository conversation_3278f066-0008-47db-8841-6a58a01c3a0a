package com.bx.implatform.mapper;

import com.bx.implatform.entity.SysConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 系统配置Mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 10:53
 */
@Mapper
public interface SysConfigMapper {
    /**
     * 新增系统配置（带有if-test）
     *
     * @param record SysConfig
     * @return 变更条数
     * <AUTHOR>
     */
    int insertSelective(SysConfig record);


    /**
     * 更新系统配置（带有if-test）
     *
     * @param record SysConfig
     * @return 变更条数
     * <AUTHOR>
     */
    int updateByPrimaryKeySelective(SysConfig record);

    /**
     * 查询所有系统设置
     *
     * @return List<SysConfig>
     * <AUTHOR>
     */
    List<SysConfig> querySysConfig();


    /**
     * 查询所有系统设置
     *
     * @return List<SysConfig>
     * <AUTHOR>
     */
    List<SysConfig> querySysConfigAll();
}