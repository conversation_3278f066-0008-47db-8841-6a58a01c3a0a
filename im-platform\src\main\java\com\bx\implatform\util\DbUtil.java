package com.bx.implatform.util;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.annotation.DbType;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * @Title: com.bx.implatform.util
 * @Package: DbUtil
 * @Description: db工具类
 * @Author: 李建奎
 * @Date: 创建时间 2025/4/29
 */
public class DbUtil {

    /**
     * 数据源
     */
    private static DataSource dataSource;

    static {
        dataSource = SpringUtil.getBean(DataSource.class);
    }


    /**
     * 获取数据库limit sql
     *
     * @param num
     * @return
     */
    public static String getLimitOneSql(int num) {

        try {
            String dbTypeName = dataSource.getConnection().getMetaData().getDatabaseProductName().toLowerCase();
            DbType dbType = DbType.getDbType(dbTypeName);
            switch (dbType) {
                case ORACLE:
                    return "AND ROWNUM = " + num;
                default:
                    return "LIMIT " + num;
            }
        } catch (SQLException e) {
            return "LIMIT " + num;
        }
    }
}