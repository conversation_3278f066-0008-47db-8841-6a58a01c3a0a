<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bx.implatform.mapper.UserDeptMapper">
    <select id="findAllDeptAndUser" resultType="com.bx.implatform.vo.UserDeptVO">
        SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",
        SUD.GROUP_NAME AS "groupName",
        SUD.ORGANIZATION_NAME AS "organizationName",
        MD.DEPT_NAME AS "deptName",
        SUD.USER_NAME AS "userName",
        SU.NICK_NAME AS "nickName",
        SU.ID AS "userId",
        SU.POST_TITLE AS "postTitle",
        SU.sex AS "sex",
        SU.head_image_thumb as "headImage",
        SU.email AS "email",
        SU.post_title AS "postTitle",
        SU.mobile AS "mobile"
        from im_user_dept SUD
        left join im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE and MD.DELETED_FLAG = '0'
        left join im_user SU ON SU.USER_NAME = SUD.USER_NAME
        where
        IFNULL(SU.NICK_NAME, ' ') != ' '
        and IFNULL(MD.DEPT_NAME, ' ') != ' '
        and SU.role in
        <foreach collection="roles" item="role" open="(" separator="," close=")">
            #{role}
        </foreach>
        and SUD.SYS_ID = 'IM'
        <if test="organizationName!= null and organizationName!= ''">
            and SUD.GROUP_NAME = #{groupName}
        </if>
        <if test="organizationName!= null and organizationName!= ''">
            and SUD.ORGANIZATION_NAME = #{organizationName}
        </if>
        <if test="deptCode!= null and deptCode!= ''">
            and SUD.DEPT_CODE = #{deptCode}
        </if>
        <if test="deptName!= null and deptName!= ''">
            and MD.DEPT_NAME = #{deptName}
        </if>
        <if test="userName!= null and userName!= ''">
            and SUD.USER_NAME = #{userName}
        </if>
        <if test="nickName!= null and nickName!= ''">
            and SU.NICK_NAME like CONCAT('%', #{nickName} , '%')
        </if>
        <if test="userNameList != null and userNameList.size() > 0">
            and SUD.USER_NAME in
            <foreach collection="userNameList" item="userName" open="(" separator="," close=")">
                #{userName}
            </foreach>
        </if>
    </select>
    <select id="findAllDept" resultType="com.bx.implatform.vo.UserDeptVO">
        SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",
        SUD.GROUP_NAME AS "groupName",
        SUD.ORGANIZATION_NAME AS "organizationName",
        MD.DEPT_NAME AS "deptName"
        from im_user_dept SUD
        left join im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE and MD.DELETED_FLAG = '0'
        where 1=1
        and IFNULL(MD.DEPT_NAME, ' ') != ' '
        and SUD.SYS_ID = 'IM'
        <if test="organizationName!= null and organizationName!= ''">
            and SUD.GROUP_NAME = #{groupName}
        </if>
        <if test="organizationName!= null and organizationName!= ''">
            and SUD.ORGANIZATION_NAME = #{organizationName}
        </if>
        <if test="deptCode!= null and deptCode!= ''">
            and SUD.DEPT_CODE = #{deptCode}
        </if>
        <if test="deptName!= null and deptName!= ''">
            and MD.DEPT_NAME = #{deptName}
        </if>
    </select>
</mapper>