package com.bx.implatform.service.thirdparty;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bx.imcommon.util.SmCryptoUtil;
import com.bx.implatform.contant.Constant;
import com.bx.implatform.entity.Group;
import com.bx.implatform.entity.GroupMember;
import com.bx.implatform.entity.GroupMessage;
import com.bx.implatform.entity.PrivateMessage;
import com.bx.implatform.entity.User;
import com.bx.implatform.enums.FileType;
import com.bx.implatform.enums.MessageType;
import com.bx.implatform.enums.ResultCode;
import com.bx.implatform.exception.GlobalException;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.IGroupMemberService;
import com.bx.implatform.service.IGroupMessageService;
import com.bx.implatform.service.IGroupService;
import com.bx.implatform.service.IPrivateMessageService;
import com.bx.implatform.service.IUserService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.util.FileUtil;
import com.bx.implatform.util.ImageUtil;
import com.bx.implatform.util.MinioUtil;
import com.bx.implatform.vo.FileMessageVO;
import com.bx.implatform.vo.UploadFileVO;
import com.bx.implatform.vo.UploadImageVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通过校验文件MD5实现重复文件秒传
 * 文件上传服务
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileService {
    private final MinioUtil minioUtil;
    @Value("${minio.public}")
    private String minIoServer;
    @Value("${minio.bucketName}")
    private String bucketName;
    @Value("${minio.imagePath}")
    private String imagePath;
    @Value("${minio.filePath}")
    private String filePath;
    @Value("${minio.videoPath}")
    private String videoPath;

    private final IPrivateMessageService privateMessageService;

    private final IUserService userService;

    private final IGroupMessageService groupMessageService;

    private final IGroupMemberService groupMemberService;

    private final IGroupService groupService;

    @PostConstruct
    public void init() {
        if (!minioUtil.bucketExists(bucketName)) {
            // 创建bucket
            minioUtil.makeBucket(bucketName);
            // 公开bucket
            minioUtil.setBucketPublic(bucketName);
        }
    }


    public String uploadFile(MultipartFile file) {
        Long userId = SessionContext.getSession().getUserId();
        // 大小校验
        if (file.getSize() > Constant.MAX_FILE_SIZE) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "文件大小不能超过10M");
        }
        // 上传
        String fileName = minioUtil.upload(bucketName, filePath, file);
        if (StringUtils.isEmpty(fileName)) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "文件上传失败");
        }
        String url = generUrl(FileType.FILE, fileName);
        log.info("文件文件成功，用户id:{},url:{}", userId, url);
        return url;
    }

    public void deleteFile(String fileName) {
        // 上传
        boolean removed = minioUtil.remove(bucketName, filePath, fileName);
        if (!removed) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "文件删除失败");
        }
        log.info("删除文件成功，fileName:{}", fileName);
    }

    public void deleteImage(String fileName) {
        // 上传
        boolean removed = minioUtil.remove(bucketName, imagePath, fileName);
        if (!removed) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "图片删除失败");
        }
        log.info("删除图片成功，fileName:{}", fileName);
    }

    public UploadImageVO uploadImage(MultipartFile file) {
        try {
            Long userId = SessionContext.getSession().getUserId();
            // 大小校验
            if (file.getSize() > Constant.MAX_IMAGE_SIZE) {
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "图片大小不能超过5M");
            }
            // 图片格式校验
            if (!FileUtil.isImage(file.getOriginalFilename())) {
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "图片格式不合法");
            }
            // 上传原图
            UploadImageVO vo = new UploadImageVO();
            String fileName = minioUtil.upload(bucketName, imagePath, file);
            if (StringUtils.isEmpty(fileName)) {
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "图片上传失败");
            }
            vo.setOriginUrl(generUrl(FileType.IMAGE, fileName));
            // 大于30K的文件需上传缩略图
            if (file.getSize() > 30 * 1024) {
                byte[] imageByte = ImageUtil.compressForScale(file.getBytes(), 30);
                fileName = minioUtil.upload(bucketName, imagePath, Objects.requireNonNull(file.getOriginalFilename()), imageByte, file.getContentType());
                if (StringUtils.isEmpty(fileName)) {
                    throw new GlobalException(ResultCode.PROGRAM_ERROR, "图片上传失败");
                }
            }
            vo.setThumbUrl(generUrl(FileType.IMAGE, fileName));
            log.info("文件图片成功，用户id:{},url:{}", userId, vo.getOriginUrl());
            return vo;
        } catch (IOException e) {
            log.error("上传图片失败，{}", e.getMessage(), e);
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "图片上传失败");
        }
    }


    public String generUrl(FileType fileTypeEnum, String fileName) {
        String url = minIoServer + "/" + bucketName;
        switch (fileTypeEnum) {
            case FILE:
                url += "/" + filePath + "/";
                break;
            case IMAGE:
                url += "/" + imagePath + "/";
                break;
            case VIDEO:
                url += "/" + videoPath + "/";
                break;
            default:
                break;
        }
        url += fileName;
        return url;
    }

    public Result getReceivedFile(String fileName, String person) {
        Long userId = SessionContext.getSession().getUserId();
        List<User> queryUsers = new ArrayList<>();
        if (StrUtil.isNotBlank(person)) {
            LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.like(User::getUserName, person).or().like(User::getNickName, person);
            queryUsers = userService.list(queryWrapper);
            if (CollUtil.isEmpty(queryUsers)) {
                return ResultUtils.success(new ArrayList<>());
            }
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> groupIds = new HashSet<>();
        List<FileMessageVO> fileMessageVOS = new ArrayList<>();
        dealReceivedPrivateFile(fileName, userId, fileMessageVOS, userIds, queryUsers);
        dealReceivedGroupFile(fileName, userId, fileMessageVOS, userIds, queryUsers, groupIds);

        Map<Long, String> userMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            List<User> list = userService.list(new LambdaQueryWrapper<User>().in(CollUtil.isNotEmpty(userIds), User::getId, userIds));
            //转成userId为key的map
            userMap = list.stream().collect(Collectors.toMap(User::getId, User::getNickName));
        }
        Map<Long, String> groupMap = new HashMap<>();
        if (CollUtil.isNotEmpty(groupIds)) {
            List<Group> groupMemberList = groupService.list(Wrappers.lambdaQuery(Group.class).in(CollUtil.isNotEmpty(groupIds), Group::getId, groupIds));
            //转成groupId为key的map
            groupMap = groupMemberList.stream().collect(Collectors.toMap(Group::getId, Group::getName));
        }
        //设置来自
        for (FileMessageVO messageVO : fileMessageVOS) {
            String sendNickName = messageVO.getSendNickName();
            if (StrUtil.isNotBlank(sendNickName)) {
                //群聊
                messageVO.setFrom(userMap.get(userId) + " | " + groupMap.get(messageVO.getGroupId()) + "的群聊");
            } else {
                //私聊
                messageVO.setFrom(userMap.get(messageVO.getSendId()) + " | " + userMap.get(messageVO.getRecvId()) + "的聊天");
            }
        }
        fileMessageVOS.sort(Comparator.comparing(FileMessageVO::getSendTime));
        return ResultUtils.success(fileMessageVOS);
    }

    private void dealReceivedPrivateFile(String fileName, Long userId, List<FileMessageVO> fileMessageVOS, Set<Long> userIds, List<User> queryUsers) {
        LambdaQueryWrapper<PrivateMessage> messageLambdaQueryWrapper = Wrappers.lambdaQuery();
        messageLambdaQueryWrapper.eq(PrivateMessage::getRecvId, userId)
                .eq(PrivateMessage::getType, MessageType.FILE.code());
        messageLambdaQueryWrapper.in(CollUtil.isNotEmpty(queryUsers), PrivateMessage::getSendId, queryUsers.stream().map(User::getId).toArray());
        FileMessageVO fileMessageVO;
        String dbFileName = "";
        List<PrivateMessage> privateMessages = privateMessageService.list(messageLambdaQueryWrapper);
        for (PrivateMessage privateMessage : privateMessages) {
            if (MessageType.FILE.code() == privateMessage.getType()) {
                String content = SmCryptoUtil.decrypt(privateMessage.getContent());
                privateMessage.setContent(content);
                if (StrUtil.isBlank(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(privateMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(privateMessage.getSendId());
                    userIds.add(privateMessage.getRecvId());
                    continue;
                }
                UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                dbFileName = uploadFileVO.getName();
                if (dbFileName.contains(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(privateMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(privateMessage.getSendId());
                    userIds.add(privateMessage.getRecvId());
                }
            }
        }
    }

    private void dealReceivedGroupFile(String fileName, Long userId, List<FileMessageVO> fileMessageVOS, Set<Long> userIds, List<User> queryUsers, Set<Long> groupIds) {
        //群消息：要看自己在不在群里面
        List<GroupMember> groupMembers = groupMemberService.list(Wrappers.lambdaQuery(GroupMember.class).eq(GroupMember::getUserId, userId).eq(GroupMember::getQuit, false));
        if (CollUtil.isEmpty(groupMembers)) {
            return;
        }
        groupIds.addAll(groupMembers.stream().map(GroupMember::getGroupId).collect(Collectors.toSet()));
        LambdaQueryWrapper<GroupMessage> groupMessageLambdaQuery = Wrappers.lambdaQuery();
        groupMessageLambdaQuery.eq(GroupMessage::getType, MessageType.FILE.code());
        groupMessageLambdaQuery.in(CollUtil.isNotEmpty(queryUsers), GroupMessage::getSendId, queryUsers.stream().map(User::getId).toArray());

        String dbFileName;
        FileMessageVO fileMessageVO;
        groupMessageLambdaQuery.in(CollUtil.isNotEmpty(groupIds), GroupMessage::getGroupId, groupIds);
        List<GroupMessage> groupMessages = groupMessageService.list(groupMessageLambdaQuery);
        for (GroupMessage groupMessage : groupMessages) {
            if (MessageType.FILE.code() == groupMessage.getType()) {
                String content = SmCryptoUtil.decrypt(groupMessage.getContent());
                groupMessage.setContent(content);
                if (StrUtil.isBlank(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(groupMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(groupMessage.getSendId());
                    continue;
                }
                UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                dbFileName = uploadFileVO.getName();
                if (dbFileName.contains(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(groupMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(groupMessage.getSendId());
                }
            }
        }
    }

    public Result getSentFile(String fileName, String person) {
        Long userId = SessionContext.getSession().getUserId();
        List<User> queryUsers = new ArrayList<>();
        if (StrUtil.isNotBlank(person)) {
            LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.like(User::getUserName, person).or().like(User::getNickName, person);
            queryUsers = userService.list(queryWrapper);
            if (CollUtil.isEmpty(queryUsers)) {
                return ResultUtils.success(new ArrayList<>());
            }
        }
        List<FileMessageVO> fileMessageVOS = new ArrayList<>();
        Set<Long> userIds = new HashSet<>();
        Set<Long> groupIds = new HashSet<>();

        dealSentPrivateFile(fileName, userId, queryUsers, fileMessageVOS, userIds);
        dealSentGroupFile(fileName, userId, queryUsers, fileMessageVOS, userIds, groupIds);

        Map<Long, String> userMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            List<User> list = userService.list(new LambdaQueryWrapper<User>().in(CollUtil.isNotEmpty(userIds), User::getId, userIds));
            //转成userId为key的map
            userMap = list.stream().collect(Collectors.toMap(User::getId, User::getNickName));
        }
        Map<Long, String> groupMap = new HashMap<>();
        if (CollUtil.isNotEmpty(groupIds)) {
            List<Group> groupMemberList = groupService.list(Wrappers.lambdaQuery(Group.class).in(CollUtil.isNotEmpty(groupIds), Group::getId, groupIds));
            //转成groupId为key的map
            groupMap = groupMemberList.stream().collect(Collectors.toMap(Group::getId, Group::getName));
        }
        //设置来自
        for (FileMessageVO messageVO : fileMessageVOS) {
            String sendNickName = messageVO.getSendNickName();
            if (StrUtil.isNotBlank(sendNickName)) {
                //群聊
                messageVO.setTo(groupMap.get(messageVO.getGroupId()) + "的群聊");
            } else {
                //私聊
                messageVO.setFrom(userMap.get(messageVO.getRecvId()));
            }
        }
        fileMessageVOS.sort(Comparator.comparing(FileMessageVO::getSendTime));
        return ResultUtils.success(fileMessageVOS);
    }

    private void dealSentPrivateFile(String fileName, Long userId, List<User> users, List<FileMessageVO> fileMessageVOS, Set<Long> userIds) {
        LambdaQueryWrapper<PrivateMessage> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(PrivateMessage::getSendId, userId)
                .eq(PrivateMessage::getType, MessageType.FILE.code())
                .in(CollUtil.isNotEmpty(users), PrivateMessage::getRecvId, users.stream().map(User::getId).toArray());
        List<PrivateMessage> privateMessages = privateMessageService.list(lambdaQuery);
        FileMessageVO fileMessageVO;
        String dbFileName;
        for (PrivateMessage privateMessage : privateMessages) {
            if (MessageType.FILE.code() == privateMessage.getType()) {
                String content = SmCryptoUtil.decrypt(privateMessage.getContent());
                privateMessage.setContent(content);
                if (StrUtil.isBlank(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(privateMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(privateMessage.getSendId());
                    userIds.add(privateMessage.getRecvId());
                    continue;
                }
                UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                dbFileName = uploadFileVO.getName();
                if (dbFileName.contains(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(privateMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(privateMessage.getSendId());
                    userIds.add(privateMessage.getRecvId());
                }
            }
        }
    }

    private void dealSentGroupFile(String fileName, Long userId, List<User> queryUsers, List<FileMessageVO> fileMessageVOS, Set<Long> userIds, Set<Long> groupIds) {
        //判断queryUsers在不在群里面
        Set<Long> queryUserIds = queryUsers.stream().map(User::getId).collect(Collectors.toSet());
        List<GroupMember> groupMembers = groupMemberService.list(Wrappers.lambdaQuery(GroupMember.class).in(CollUtil.isNotEmpty(queryUserIds), GroupMember::getUserId, queryUserIds).eq(GroupMember::getQuit, false));
        if (CollUtil.isEmpty(groupMembers)) {
            return;
        }
        groupIds.addAll(groupMembers.stream().map(GroupMember::getGroupId).collect(Collectors.toSet()));
        String dbFileName;
        FileMessageVO fileMessageVO;
        LambdaQueryWrapper<GroupMessage> groupMessageLambdaQuery = Wrappers.lambdaQuery();
        groupMessageLambdaQuery.eq(GroupMessage::getType, MessageType.FILE.code());
        groupMessageLambdaQuery.eq(GroupMessage::getSendId, userId);
        List<GroupMessage> groupMessages = groupMessageService.list(groupMessageLambdaQuery);
        for (GroupMessage groupMessage : groupMessages) {
            if (MessageType.FILE.code() == groupMessage.getType()) {
                String content = SmCryptoUtil.decrypt(groupMessage.getContent());
                groupMessage.setContent(content);
                if (StrUtil.isBlank(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(groupMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(groupMessage.getSendId());
                    groupIds.add(groupMessage.getGroupId());
                    continue;
                }
                UploadFileVO uploadFileVO = JSONUtil.toBean(content, UploadFileVO.class);
                dbFileName = uploadFileVO.getName();
                if (dbFileName.contains(fileName)) {
                    fileMessageVO = BeanUtil.copyProperties(groupMessage, FileMessageVO.class);
                    fileMessageVOS.add(fileMessageVO);
                    userIds.add(groupMessage.getSendId());
                    groupIds.add(groupMessage.getGroupId());
                }
            }
        }
    }
}
