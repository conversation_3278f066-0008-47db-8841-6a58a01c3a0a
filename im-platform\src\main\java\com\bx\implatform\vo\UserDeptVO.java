package com.bx.implatform.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 用户部门关系表
 *
 * @TableName im_user_dept
 */
@ApiModel("用户部门关系表VO")
@Data
public class UserDeptVO implements Serializable {
    /**
     * 用户科室id
     */
    @ApiModelProperty(value = "用户科室id")
    private Integer userDeptId;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    private String groupName;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户名称（账号）
     */
    @ApiModelProperty(value = "用户名称（账号）")
    private String userName;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String postTitle;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String headImage;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private Integer sex;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 科室编码
     */
    @ApiModelProperty(value = "科室编码")
    private String deptCode;

    /**
     * 科室名称
     */
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    private String sysId;

    @ApiModelProperty(value = "扩展字段",hidden = true)
    private Map<String,Object> extMap;

}
