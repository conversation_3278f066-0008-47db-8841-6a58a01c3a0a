CREATE TABLE `sys_config`
(
    `id`           int NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `module_id`    varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '模块ID',
    `config_code`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '配置编码',
    `config_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '配置名称',
    `description`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置说明',
    `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '属性值',
    `config_type`  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci       DEFAULT NULL COMMENT '配置类型 1：文本 2：布尔值 3：数字 4：日期',
    `sort_code`    int                                                            DEFAULT NULL COMMENT '排序',
    `deleted_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci       DEFAULT '0' COMMENT '删除标志 0：否 1：是',
    `create_by`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '创建人',
    `create_time`  datetime                                                       DEFAULT NULL COMMENT '创建时间',
    `update_by`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '修改人',
    `update_time`  datetime                                                       DEFAULT NULL COMMENT '修改时间',
    `sys_id`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '系统ID',
    `web_show`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci       DEFAULT 'Y' COMMENT '界面是否展示 Y：是 N：否',
    `module_name`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '模块名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置表';


INSERT INTO SYS_CONFIG (MODULE_ID, CONFIG_CODE, CONFIG_NAME, DESCRIPTION, CONFIG_VALUE, CONFIG_TYPE, SORT_CODE, DELETED_FLAG, CREATE_TIME, SYS_ID, WEB_SHOW, MODULE_NAME)
VALUES ('1', 'config.msg.file.retention.time', '消息附件保留时间', '消息附件文件存储时长，单位天，默认90天', '90', '3', '2', '0', NOW(), 'SSO', 'Y', '设置');