package com.bx.implatform.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSONObject;
import com.bx.implatform.entity.SysConfig;
import com.bx.implatform.mapper.SysConfigMapper;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.SystemSettingService;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 系统设置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 13:48
 */
@Service
@Slf4j
@Transactional
public class SystemSettingImpl implements SystemSettingService {
    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Override
    public Result querySysConfig() {
        List<SysConfig> list = sysConfigMapper.querySysConfig();
        Map<String, List<SysConfig>> map = new HashMap<>();
        //按照模块划分
        if (list != null) {
            map = list.stream().filter(sysConfig -> "Y".equals(sysConfig.getWebShow())).collect(Collectors.groupingBy(SysConfig::getModuleId));
        }
        return ResultUtils.success(map);
    }

    /**
     * 添加系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @Override
    public Result addSysConfig(List<SysConfig> sysConfigList) {
        //校验参数
        if (CollUtil.isEmpty(sysConfigList)) {
            throw new RuntimeException("参数为空！");
        }
        Date date = new Date();
        sysConfigList.stream().forEach(sysConfig -> {
            sysConfig.setCreateTime(date);
            sysConfig.setCreateBy("");
            sysConfigMapper.insertSelective(sysConfig);
        });
        return ResultUtils.success();
    }

    /**
     * 更新系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @Override
    public Result updateSysConfigs(List<SysConfig> sysConfigList) {
        //校验参数
        if (CollUtil.isEmpty(sysConfigList)) {
            throw new RuntimeException("配置参数为空！");
        }
        //数据库目前配置信息
        List<SysConfig> sysConfigs = sysConfigMapper.querySysConfigAll();
        //页面全部的配置信息
        Set<SysConfig> setNew = Sets.newHashSet(sysConfigList);
        Set<SysConfig> setOld = Sets.newHashSet(sysConfigs);
        Set<SysConfig> difference = Sets.difference(setNew, setOld);
        if (CollUtil.isEmpty(difference)) {
            throw new RuntimeException("配置参数没有变化！！！");
        }
        Date date = new Date();
        difference.stream().forEach(sysConfig -> {
            if ("6".equals(sysConfig.getConfigType()) || "7".equals(sysConfig.getConfigType())) {
                //如果类型为6/7 单独处理下JSON串
                try {
                    String jsonStrTemp = HtmlUtil.unescape(sysConfig.getConfigValue());
                    JSONObject tempJson = JSONObject.parseObject(jsonStrTemp);
                    sysConfig.setConfigValue(tempJson.toJSONString());
                } catch (Exception e) {
                    log.error("字符串转义失败", e);
                }
            }
            sysConfig.setUpdateTime(date);
            sysConfig.setUpdateBy("");
            sysConfigMapper.updateByPrimaryKeySelective(sysConfig);
        });
        return ResultUtils.success();
    }

    @Override
    public Result queryAllSysConfig() {
        List<SysConfig> list = sysConfigMapper.querySysConfig();
        Map<String, Object> collect = list.stream().filter(sysConfig -> StrUtil.isNotBlank(sysConfig.getConfigValue())).collect(Collectors.toMap(sysConfig -> sysConfig.getConfigCode(), sysConfig -> sysConfig.getConfigValue() == null ? "" : sysConfig.getConfigValue(), (n1, n2) -> n2, HashMap<String, Object>::new));
        return ResultUtils.success(collect);
    }

    @Override
    public Result queryCommonSysConfig() {
        List<SysConfig> list = sysConfigMapper.querySysConfig();
        return ResultUtils.success(list);
    }
}