package com.bx.implatform.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 用户部门关系表
 *
 * @TableName im_user_dept
 */
@ApiModel("用户部门关系表DTO")
@TableName(value = "im_user_dept")
@Data
public class UserDeptDTO implements Serializable {
    /**
     * 集团编码
     */
    @ApiModelProperty(value = "集团编码")
    private String groupName;

    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    private String organizationName;

    /**
     * 用户名称（账号）
     */
    @ApiModelProperty(value = "用户名称（账号）")
    private String userName;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称，支持模糊查询")
    private String nickName;

    /**
     * 科室编码
     */
    @ApiModelProperty(value = "科室编码")
    private String deptCode;

    /**
     * 科室名称
     */
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    /**
     * 用户角色，固定三种角色：管理员、普通用户、临时用户
     */
    @ApiModelProperty(value = "用户角色，固定三种角色：管理员、普通用户、临时用户")
    private List<String> roles = Arrays.asList("管理员", "普通用户");

    List<String> userNameList;

    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    private String sysId = "IM";

}
