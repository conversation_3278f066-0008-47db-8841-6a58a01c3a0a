package com.bx.implatform.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 部门信息
 *
 * @TableName im_dept
 */
@TableName(value = "im_dept")
@ApiModel("部门信息")
@Data
public class Dept implements Serializable {
    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    @TableField("dept_code")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField("dept_name")
    private String deptName;

    /**
     * 删除标记
     */
    @ApiModelProperty(value = "删除标记")
    @TableField("deleted_flag")
    private String deletedFlag;

}
