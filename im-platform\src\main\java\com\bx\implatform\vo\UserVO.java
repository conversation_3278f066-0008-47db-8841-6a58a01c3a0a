package com.bx.implatform.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bx.implatform.entity.UserDept;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("用户信息VO")
public class UserVO {

    @NotNull(message = "用户id不能为空")
    @ApiModelProperty(value = "id")
    private Long id;

    @NotEmpty(message = "用户名不能为空")
    @Length(max = 64, message = "用户名不能大于64字符")
    @ApiModelProperty(value = "用户名")
    private String userName;

    @NotEmpty(message = "用户昵称不能为空")
    @Length(max = 64, message = "昵称不能大于64字符")
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "用户类型 1:普通用户 2:审核账户")
    private Integer type;

    @Length(max = 1024, message = "个性签名不能大于1024个字符")
    @ApiModelProperty(value = "个性签名")
    private String signature;

    @ApiModelProperty(value = "头像")
    private String headImage;

    @ApiModelProperty(value = "头像缩略图")
    private String headImageThumb;

    @ApiModelProperty(value = "是否在线")
    private Boolean online;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String postTitle;

    /**
     * 过期时间，天数，针对临时用户
     */
    @ApiModelProperty(value = "过期时间，天数，针对临时用户")
    private Long expireDays;

    /**
     * 用户角色，固定三种角色：管理员、普通用户、临时用户
     */
    @NotBlank(message = "用户角色不可为空")
    @ApiModelProperty(value = "用户角色，固定三种角色：管理员、普通用户、临时用户")
    private String role = "普通用户";

    /**
     * 临时用户聊天对象Id
     */
    @ApiModelProperty(value = "临时用户聊天对象Id")
    @TableField("chat_object_ids")
    private String chatObjectIds;

    /**
     * 用户部门信息
     */
    @ApiModelProperty(value = "用户部门信息")
    List<UserDeptVO> userDepts;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    List<UserDeptVO> connectionPersons;
}
