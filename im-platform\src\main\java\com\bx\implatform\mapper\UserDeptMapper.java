package com.bx.implatform.mapper;

import com.bx.implatform.dto.UserDeptDTO;
import com.bx.implatform.entity.UserDept;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bx.implatform.vo.UserDeptVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【im_user_dept(用户部门关系表)】的数据库操作Mapper
* @createDate 2024-05-21 15:19:27
* @Entity com.bx.implatform.entity.UserDept
*/
@Mapper
public interface UserDeptMapper extends BaseMapper<UserDept> {

    List<UserDeptVO> findAllDeptAndUser(UserDeptDTO userDeptDTO);

    List<UserDeptVO> findAllDept(UserDeptDTO userDeptDTO);
}
