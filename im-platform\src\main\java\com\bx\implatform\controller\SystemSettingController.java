package com.bx.implatform.controller;

import com.bx.implatform.entity.SysConfig;
import com.bx.implatform.result.Result;
import com.bx.implatform.service.SystemSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统设置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 13:47
 */
@Api(tags = {"系统设置"})
@RestController
@RequestMapping("/system")
public class SystemSettingController {

    @Autowired
    private SystemSettingService systemSettingService;

    /**
     * 获取所有系统设置（webShow为Y的）
     *
     * @return 系统设置
     * <AUTHOR>
     */
    @ApiOperation(value = "获取所有系统设置（webShow为Y的）", notes = "")
    @GetMapping("/querySysConfig")
    public Result querySysConfig() {
        return systemSettingService.querySysConfig();
    }

    /**
     * 获取所有系统设置（包括webShow为N和Y的）
     *
     * @return 系统设置
     * <AUTHOR>
     */
    @ApiOperation(value = "获取所有系统设置（包括webShow为N和Y的）", notes = "")
    @GetMapping("/queryAllSysConfig")
    public Result queryAllSysConfig() {
        return systemSettingService.queryAllSysConfig();
    }

    /**
     * 获取系统设置（sys_config中MODULE_ID=1）
     *
     * @return 系统设置
     * <AUTHOR>
     */
    @ApiOperation(value = "获取系统设置（只有模块1下面的）", notes = "")
    @GetMapping("/queryCommonSysConfig")
    public Result queryCommonSysConfig() {
        return systemSettingService.queryCommonSysConfig();
    }

    /**
     * 添加系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "添加系统设置", notes = "")
    //@LogRemark(operate = "添加系统设置", module = "系统设置")
    @PostMapping("/addSysConfigs")
    public Result addSysConfigs(@RequestBody List<SysConfig> sysConfigList) {
        return systemSettingService.addSysConfig(sysConfigList);
    }

    /**
     * 更新系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    @ApiOperation(value = "更新系统设置", notes = "")
    //@LogRemark(operate = "更新系统设置", module = "系统设置")
    @PostMapping("/updateSysConfigs")
    public Result updateSysConfigs(@RequestBody List<SysConfig> sysConfigList) {
        return systemSettingService.updateSysConfigs(sysConfigList);
    }
}
