
## minio部署
```bash
docker run \
--network host \
--name minio \
-d --restart=always \
--privileged=true \
-e "MINIO_ACCESS_KEY=admin" \
-e "MINIO_SECRET_KEY=Jykj1994@" \
-v /home/<USER>/minio/data:/data \
 minio/minio server /data --console-address ":9002" -address ":9001"
```



## 前端docker
```
docker run \
--name=nginxim \
--privileged=true \
--restart=always \
--network=host \
-v /home/<USER>/im-ui/dist:/usr/share/nginx/html \
-v /home/<USER>/im-ui/ssl:/etc/nginx/ssl \
-v /home/<USER>/im-ui/nginx.conf:/etc/nginx/nginx.conf \
-v /home/<USER>/im-ui/logs:/var/log/nginx \
-d nginx
```
docker run --name=nginxim --privileged=true --restart=always --network=host 
-v /home/<USER>/im-ui:/etc/nginx/im-ui 
-v /home/<USER>/cjodrapphtml:/home/<USER>/cjodrapphtml 
-v /home/<USER>/ssl:/etc/nginx/ssl 
-v /home/<USER>/nginx.conf:/etc/nginx/nginx.conf 
-v /home/<USER>/conf.d:/etc/nginx/conf.d 
-v /home/<USER>/html:/usr/share/nginx/html 
-v /root/nginx/logs:/var/log/nginx 
-d nginx
