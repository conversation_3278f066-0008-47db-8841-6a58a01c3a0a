1747016937404|225|statement|connection 0|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'huangjie')
1747016938064|26|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747016938240|149|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747016938330|13|statement|connection 0|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747016938407|50|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747016938418|73|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747016938418|68|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747016938449|7|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T10:28:58.423+0800')
1747016938449|7|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T10:28:58.423+0800')
1747016938477|17|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747016938478|17|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747016962988|12|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747016963043|49|statement|connection 1|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 51 AND status = 3)
1747016966813|90|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, '931fdcc071f28a98f4cb7c94487ce85c', 0, 0, '2025-05-12T10:29:26.593+0800' )
1747016969390|4|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, '2d5cd5dd4883bedbb4cea2f534a9ca26', 0, 0, '2025-05-12T10:29:29.377+0800' )
1747016973114|4|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, '0ef7b2d36a3b8ba8d9813aa960177e49', 0, 0, '2025-05-12T10:29:33.102+0800' )
1747016978548|5|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747016978553|3|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747016978703|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747016978715|7|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747016978716|8|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747016978718|5|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T10:29:38.708+0800')
1747016978721|4|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747016978724|4|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747016978729|11|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747016978732|3|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 51 AND status = 3)
1747016978736|4|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T10:29:38.726+0800')
1747016978749|8|statement|connection 3|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747016992071|7|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, '881b800f7485f153cdf4badd4fd7956a5712d534034533cd8c15c994ed766ac598282db39b7baa9bb20a0ca7d772b9b201517d7d9d11dd1647df839ca47cb867631cc4834fce094b04f550504cbf19f01688daac85e052fd8335883503d367e55a94a11e2af68fa12dcc3b56b3961074ef4fb983d8ae20a41ded5d59d6d9e05d323fc54f5c1d6509bdec5e6093e70a1aa5022ab96cf3b588fd5dad8dd6b90be993cc5a488abb61cdc128cbe4a3c48be53672b9174adf729e482b998faec21cf0', 1, 0, '2025-05-12T10:29:52.056+0800' )
1747017040368|7|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (group_id = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (group_id = 2)
1747017040379|6|statement|connection 0|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (id IN (?,?,?))|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (id IN (1,33,36))
1747017053630|4|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747017053646|10|statement|connection 4|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747017054122|6|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747017054124|4|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747017054124|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747017054133|12|statement|connection 3|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747017054135|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T10:30:54.126+0800')
1747017054135|6|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T10:30:54.124+0800')
1747017054146|7|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747017054146|7|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747020505563|22|statement|connection 14|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (911) AND status = 0)
1747020506756|2|statement|connection 14|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (912) AND status = 0)
1747020508999|2|statement|connection 14|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (913) AND status = 0)
1747020512990|4|statement|connection 17|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (914) AND status = 0)
1747020517871|3|statement|connection 20|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (915) AND status = 0)
1747020519796|2|statement|connection 20|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (916) AND status = 0)
1747020522768|2|statement|connection 20|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (917) AND status = 0)
1747020538126|5|statement|connection 20|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (918) AND status = 0)
1747021235352|90|statement|connection 0|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'huangjie')
1747021235808|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747021235832|5|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747021236199|12|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747021236217|8|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747021236238|7|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T11:40:36.206+0800')
1747021236240|6|statement|connection 1|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747021236241|6|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747021236258|18|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T11:40:36.222+0800')
1747021236259|27|statement|connection 3|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747021236274|26|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747021236275|13|statement|connection 2|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747021236321|20|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 51 AND status = 3) ORDER BY id DESC
1747021260215|3|statement|connection 1|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'admin')
1747021260374|5|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747021260386|9|statement|connection 4|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747021260424|5|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747021260436|8|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T11:41:00.424+0800')
1747021260439|11|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747021260440|3|statement|connection 3|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747021260444|7|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747021260452|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T11:41:00.445+0800')
1747021271747|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747021271787|13|statement|connection 1|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747021274392|14|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '931fdcc071f28a98f4cb7c94487ce85c', 0, 0, '2025-05-12T11:41:14.361+0800' )
1747021276245|38|statement|connection 4|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '2d5cd5dd4883bedbb4cea2f534a9ca26', 0, 0, '2025-05-12T11:41:16.190+0800' )
1747021316429|4|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '0ef7b2d36a3b8ba8d9813aa960177e49', 0, 0, '2025-05-12T11:41:56.410+0800' )
1747028249236|5|statement|connection 30|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747028249251|6|statement|connection 30|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747028250103|14|statement|connection 30|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747028250103|13|statement|connection 33|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028250103|26|statement|connection 31|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747028250104|14|statement|connection 32|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028250129|14|statement|connection 33|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:37:30.111+0800')
1747028250129|17|statement|connection 32|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:37:30.107+0800')
1747028250137|7|statement|connection 30|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747028250137|4|statement|connection 32|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747028250143|8|statement|connection 33|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028250154|12|statement|connection 30|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028300654|7|statement|connection 30|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747028300667|6|statement|connection 30|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747028301612|5|statement|connection 30|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028301616|4|statement|connection 31|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747028301618|6|statement|connection 33|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028301635|23|statement|connection 32|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747028301636|11|statement|connection 33|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:38:21.620+0800')
1747028301637|21|statement|connection 30|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747028301651|21|statement|connection 31|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747028301665|13|statement|connection 34|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:38:21.615+0800')
1747028301665|24|statement|connection 33|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028301684|16|statement|connection 34|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028337834|4|statement|connection 33|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747028337846|8|statement|connection 33|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747028338511|3|statement|connection 33|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028338520|4|statement|connection 36|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747028338520|2|statement|connection 33|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:38:58.514+0800')
1747028338523|12|statement|connection 35|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747028338528|9|statement|connection 38|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028338535|10|statement|connection 33|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028338538|4|statement|connection 38|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:38:58.531+0800')
1747028338551|9|statement|connection 38|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028338599|4|statement|connection 35|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747028338610|15|statement|connection 33|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747028361864|3|statement|connection 36|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'admin')
1747028362009|7|statement|connection 35|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747028362016|4|statement|connection 35|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747028362050|6|statement|connection 35|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747028362050|5|statement|connection 36|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747028362059|8|statement|connection 38|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747028362059|5|statement|connection 36|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T13:39:22.051+0800')
1747028362062|9|statement|connection 39|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747028362065|4|statement|connection 38|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T13:39:22.059+0800')
1747028638128|11|statement|connection 40|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747028638140|7|statement|connection 40|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747028638967|6|statement|connection 41|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028638971|6|statement|connection 42|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747028638971|9|statement|connection 40|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747028650613|11635|statement|connection 42|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747028650613|11641|statement|connection 43|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028650614|11635|statement|connection 41|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:43:58.971+0800')
1747028650621|4|statement|connection 41|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028650624|5|statement|connection 43|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:44:10.616+0800')
1747028650632|4|statement|connection 43|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028650847|14|statement|connection 40|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747028677964|3|statement|connection 40|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747028677972|4|statement|connection 40|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747028678377|9|statement|connection 40|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028678378|6|statement|connection 42|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028678378|4|statement|connection 41|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747028678388|6|statement|connection 42|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:44:38.379+0800')
1747028678388|13|statement|connection 43|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747028678388|6|statement|connection 40|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:44:38.379+0800')
1747028678398|7|statement|connection 42|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028678398|7|statement|connection 40|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028693572|2|statement|connection 43|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747028693572|3|statement|connection 40|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747028695364|9|statement|connection 40|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '931fdcc071f28a98f4cb7c94487ce85c', 0, 0, '2025-05-12T13:44:55.348+0800' )
1747028697013|5|statement|connection 40|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '2d5cd5dd4883bedbb4cea2f534a9ca26', 0, 0, '2025-05-12T13:44:56.987+0800' )
1747028699511|4|statement|connection 40|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '916ab7d5335bbc9683e89fbf3f7ce4ec', 0, 0, '2025-05-12T13:44:59.500+0800' )
1747028703013|4|statement|connection 41|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747028703028|5|statement|connection 41|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747028703190|7|statement|connection 41|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028703205|11|statement|connection 41|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:45:03.192+0800')
1747028703236|28|statement|connection 41|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747028703297|20|statement|connection 43|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747028703302|13|statement|connection 41|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747028703302|20|statement|connection 40|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747028703305|3|statement|connection 43|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747028703313|6|statement|connection 40|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:45:03.304+0800')
1747028703315|15|statement|connection 42|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747028703326|9|statement|connection 40|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747029111026|271|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747029111173|5|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747029111427|7|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747029111512|6|statement|connection 3|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747029111513|9|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747029111518|4|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747029111523|8|statement|connection 1|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747029111544|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:51:51.519+0800')
1747029111544|4|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:51:51.525+0800')
1747029111559|4|statement|connection 2|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747029111559|4|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747029111627|9|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747029212660|7|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747029212674|10|statement|connection 3|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747029212709|4|statement|connection 3|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747029212712|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747029212713|5|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747029212714|6|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747029212723|2|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T13:53:32.716+0800')
1747029212726|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T13:53:32.716+0800')
1747029214960|2|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747029215064|59|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T13:53:34.991+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC
1747029221534|4|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747029221565|5|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747029221671|13|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (27,28,29,30) AND status = 0)
1747029223228|6|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747029223247|11|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 30 AND send_time >= '2025-04-12T13:53:43.220+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1))) ORDER BY id DESC
1747029223337|69|statement|connection 3|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?) ORDER BY id DESC|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 0 AND send_time > '2025-04-12T13:53:43.251+0800' AND group_id IN (2,3,4,5) AND status <> 2) ORDER BY id DESC
1747029223354|11|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:53:43.339+0800')
1747029245063|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747029245066|5|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747029245066|4|statement|connection 1|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3) ORDER BY id DESC
1747029245069|3|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747029245186|10|statement|connection 3|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747029245194|5|commit|connection 3|url **********************:@*************:1521/MIPDW||
1747029245326|10|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747029245329|6|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747029245330|2|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747029245343|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:54:05.335+0800')
1747029245352|4|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747029245352|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747029245361|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:54:05.355+0800')
1747029245367|2|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747029245427|4|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?) ORDER BY id DESC|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3) ORDER BY id DESC
1747029245427|6|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747029245611|2|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747029245618|5|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 30 AND send_time >= '2025-04-12T13:54:05.609+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1))) ORDER BY id DESC
1747029245629|7|statement|connection 3|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?) ORDER BY id DESC|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 0 AND send_time > '2025-04-12T13:54:05.618+0800' AND group_id IN (2,3,4,5) AND status <> 2) ORDER BY id DESC
1747029245639|3|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T13:54:05.632+0800')
1747029253393|19|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'f1ebcef9b99c0237948e4a7106eea82a', 0, 0, '2025-05-12T13:54:13.351+0800' )
1747029264897|3|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747029264900|1|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747029270384|9|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '931fdcc071f28a98f4cb7c94487ce85c', 0, 0, '2025-05-12T13:54:30.365+0800' )
1747029273825|5|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '2d5cd5dd4883bedbb4cea2f534a9ca26', 0, 0, '2025-05-12T13:54:33.809+0800' )
1747029276055|9|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '0ef7b2d36a3b8ba8d9813aa960177e49', 0, 0, '2025-05-12T13:54:36.030+0800' )
1747029277307|4|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747029277324|14|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747029283874|6|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '916ab7d5335bbc9683e89fbf3f7ce4ec', 0, 0, '2025-05-12T13:54:43.858+0800' )
1747029284006|16|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747029285079|7|statement|connection 3|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747029285083|2|commit|connection 3|url **********************:@*************:1521/MIPDW||
1747029287825|4|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '00377ae61ef13df2c0d53a77a77bea52', 0, 0, '2025-05-12T13:54:47.813+0800' )
1747029290060|9|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '64ce45cf7057b36bb207ec2da01295dc', 0, 0, '2025-05-12T13:54:50.043+0800' )
1747029292587|4|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, 'ecbe08e418d2154a164d384321be1915', 0, 0, '2025-05-12T13:54:52.576+0800' )
1747029292803|8|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (38) AND status = 0)
1747029295410|5|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '28f9774c3028528c7e634365ddd3a932', 0, 0, '2025-05-12T13:54:55.397+0800' )
1747029298796|5|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '7d8c87de95bdfadb45bf9c02d3c5510d', 0, 0, '2025-05-12T13:54:58.780+0800' )
1747029299888|7|statement|connection 3|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747029299893|3|commit|connection 3|url **********************:@*************:1521/MIPDW||
1747029303699|6|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '2805d862b37ebc5c88aa20a3a9ea1e76', 0, 0, '2025-05-12T13:55:03.687+0800' )
1747029320250|6|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '881b800f7485f153cdf4badd4fd7956a5712d534034533cd8c15c994ed766ac598282db39b7baa9bb20a0ca7d772b9b201517d7d9d11dd1647df839ca47cb86775d5afbf31be2178fd0af0a2b8ae308087c043bacc647aaf95148ce02035fe845a94a11e2af68fa12dcc3b56b3961074ef4fb983d8ae20a41ded5d59d6d9e05d323fc54f5c1d6509bdec5e6093e70a1aa5022ab96cf3b588fd5dad8dd6b90be96fcd9eb264b3b74ca7854f63c22ca99d3672b9174adf729e482b998faec21cf0', 1, 0, '2025-05-12T13:55:20.232+0800' )
1747029320431|19|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (42) AND status = 0)
1747029324118|14|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '0ca5f44f639d68fcbaa78a0e007f27f3', 0, 0, '2025-05-12T13:55:24.086+0800' )
1747029325612|3|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747029325615|2|commit|connection 0|url **********************:@*************:1521/MIPDW||
1747029329569|5|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '7932d27ef49bedcdba8558e2bd46c523', 0, 0, '2025-05-12T13:55:29.557+0800' )
1747029335135|4|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '31c4368713686e322662daa96cf74d15', 0, 0, '2025-05-12T13:55:35.116+0800' )
1747029338189|3|statement|connection 2|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747029338192|2|commit|connection 2|url **********************:@*************:1521/MIPDW||
1747030601765|17|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (912,913,914,915,916,917,918,911) AND status = 0)
1747030618110|3|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (923) AND status = 0)
1747030895745|12|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747030895759|13|commit|connection 0|url **********************:@*************:1521/MIPDW||
1747037614927|240|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747037615085|6|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747037615571|7|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747037615581|7|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747037615581|12|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747037615590|15|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747037615614|8|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:13:35.587+0800')
1747037615621|15|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:13:35.596+0800')
1747037615634|8|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747037615634|10|statement|connection 3|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747037615655|13|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747037615765|13|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747037618475|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747037618499|25|statement|connection 1|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 927 AND send_time >= '2025-04-12T16:13:38.455+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747037618526|20|statement|connection 0|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:13:38.495+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747037618546|13|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:13:38.528+0800')
1747037622073|16|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747037622073|18|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747037629392|11|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'e8386acf25aa41a5d006720e31d42320', 0, 0, '2025-05-12T16:13:49.364+0800' )
1747037647596|6|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '5c1ebad7413b3b77fb5ac164ef014252', 0, 0, '2025-05-12T16:14:07.581+0800' )
1747037658798|10|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'e07324494a936780761057bba8c59ce7', 0, 0, '2025-05-12T16:14:18.775+0800' )
1747037680993|7|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747037681010|11|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747037681761|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747037681763|5|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747037681765|4|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747037681788|18|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:14:41.766+0800')
1747037681797|30|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747037681800|7|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747037681803|18|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747037681809|5|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:14:41.800+0800')
1747037681813|6|statement|connection 4|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747037681846|27|statement|connection 3|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747037682381|7|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747037682396|7|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 927 AND send_time >= '2025-04-12T16:14:42.376+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747037682416|14|statement|connection 1|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:14:42.395+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747037682429|7|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:14:42.417+0800')
1747037891307|6|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '347c3a0b044bee159c892c7aeffeb54d', 0, 0, '2025-05-12T16:18:11.280+0800' )
1747037985787|20|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (919,920,921,922,923,924,925) AND status = 0)
1747038029121|9|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (928,930,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927) AND status = 0)
1747038039659|4|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (931) AND status = 0)
1747038041499|2|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (932) AND status = 0)
1747038044589|2|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (933) AND status = 0)
1747038045884|4|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (934) AND status = 0)
1747038055176|4|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (936) AND status = 0)
1747038056898|2|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (937) AND status = 0)
1747038060248|2|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (938) AND status = 0)
1747038064095|4|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (939) AND status = 0)
1747038153862|5|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038153871|5|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747038154419|4|statement|connection 0|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747038154425|11|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038154429|14|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038154439|11|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038154439|7|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:22:34.427+0800')
1747038154456|12|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038154456|8|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:22:34.442+0800')
1747038154472|13|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038154798|7|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038154808|13|statement|connection 2|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:22:34.790+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747038154830|12|statement|connection 4|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 0 AND send_time > '2025-04-12T16:22:34.813+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747038154841|6|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:22:34.832+0800')
1747038154917|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038154952|4|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038155060|35|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (45) AND status = 0)
1747038155276|15|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (37,38,42,43) AND status = 0)
1747038155455|15|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (32,34,29) AND status = 0)
1747038155588|22|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747038155648|22|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (22,23) AND status = 0)
1747038155652|3|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747038155677|4|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747038155698|3|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747038179393|13|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747038179393|14|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038179498|5|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747038179510|8|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747038199230|10|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'fed11f94d682a0ace09d54d090562bbf', 0, 0, '2025-05-12T16:23:19.212+0800' )
1747038202795|18|statement|connection 4|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'f21d7181f089cec5b364c13d1e4b742f', 0, 0, '2025-05-12T16:23:22.766+0800' )
1747038225715|3|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038225725|5|statement|connection 3|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747038226012|11|statement|connection 3|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038226017|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038226018|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038226018|5|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747038226024|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:23:46.019+0800')
1747038226027|6|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:23:46.019+0800')
1747038226030|3|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038226035|6|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038226099|7|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038226099|7|statement|connection 3|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747038226736|5|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038226752|11|statement|connection 2|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 51 AND send_time >= '2025-04-12T16:23:46.729+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747038226754|6|statement|connection 3|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:23:46.745+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747038226763|6|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:23:46.755+0800')
1747038249566|3|statement|connection 1|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'admin')
1747038249708|2|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038249717|4|statement|connection 3|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038249753|5|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038249761|7|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:24:09.753+0800')
1747038249764|9|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038249771|5|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038249771|5|statement|connection 4|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038249775|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:24:09.771+0800')
1747038249870|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038249878|7|statement|connection 3|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:24:09.869+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038250016|4|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038250028|2|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038250043|4|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038250186|2|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (34) AND status = 0)
1747038251339|5|statement|connection 2|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038251344|8|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038251369|6|statement|connection 3|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038251375|4|commit|connection 3|url **********************:@*************:1521/MIPDW||
1747038258988|10|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038258988|6|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038260264|10|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038260276|8|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038260438|7|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038260465|14|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038260465|14|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038260465|19|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038260466|20|statement|connection 1|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038260465|13|statement|connection 4|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038260472|2|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:24:20.466+0800')
1747038260472|4|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:24:20.465+0800')
1747038260561|7|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038260573|5|statement|connection 2|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 51 AND send_time >= '2025-04-12T16:24:20.565+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038266636|5|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '5aed15e83a4c65f7466a163789e10398', 0, 0, '2025-05-12T16:24:26.614+0800' )
1747038266740|3|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038266763|6|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (52) AND status = 0)
1747038267505|6|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038267508|3|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747038271249|4|statement|connection 4|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '2805d862b37ebc5c88aa20a3a9ea1e76', 0, 0, '2025-05-12T16:24:31.238+0800' )
1747038271369|3|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038273847|5|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, 'e8386acf25aa41a5d006720e31d42320', 0, 0, '2025-05-12T16:24:33.836+0800' )
1747038275848|5|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '5c1ebad7413b3b77fb5ac164ef014252', 0, 0, '2025-05-12T16:24:35.835+0800' )
1747038276182|3|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747038276188|4|commit|connection 0|url **********************:@*************:1521/MIPDW||
1747038278498|5|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'e07324494a936780761057bba8c59ce7', 0, 0, '2025-05-12T16:24:38.485+0800' )
1747038280666|8|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'c368bb4f25b5c929d7d0563e1cc96f5d', 0, 0, '2025-05-12T16:24:40.646+0800' )
1747038283998|15|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'f8563cda6dc39bb4d7c827f10ac58edd', 0, 0, '2025-05-12T16:24:43.973+0800' )
1747038284909|3|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038284912|2|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747038287801|5|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '46725aa1688ed7f1bccf0989aa0e0ec5', 0, 0, '2025-05-12T16:24:47.789+0800' )
1747038288854|2|statement|connection 3|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747038288856|2|commit|connection 3|url **********************:@*************:1521/MIPDW||
1747038296929|4|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '931fdcc071f28a98f4cb7c94487ce85c', 0, 0, '2025-05-12T16:24:56.917+0800' )
1747038298139|2|statement|connection 2|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038298141|1|commit|connection 2|url **********************:@*************:1521/MIPDW||
1747038300531|2|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038300539|3|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747038300881|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038300883|2|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747038300885|3|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038300887|3|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:25:00.882+0800')
1747038300889|8|statement|connection 4|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038300892|3|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:25:00.887+0800')
1747038300893|4|statement|connection 1|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038300902|9|statement|connection 3|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038300987|6|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038301009|7|statement|connection 2|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747038301618|10|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038301637|17|statement|connection 3|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 60 AND send_time >= '2025-04-12T16:25:01.606+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747038301658|11|statement|connection 1|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:25:01.644+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747038301671|8|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:25:01.659+0800')
1747038304104|26|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '2d5cd5dd4883bedbb4cea2f534a9ca26', 0, 0, '2025-05-12T16:25:04.071+0800' )
1747038307139|4|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '0ef7b2d36a3b8ba8d9813aa960177e49', 0, 0, '2025-05-12T16:25:07.128+0800' )
1747038309150|6|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '916ab7d5335bbc9683e89fbf3f7ce4ec', 0, 0, '2025-05-12T16:25:09.137+0800' )
1747038310328|4|statement|connection 4|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '00377ae61ef13df2c0d53a77a77bea52', 0, 0, '2025-05-12T16:25:10.318+0800' )
1747038310914|3|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038310919|4|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747038313119|5|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '64ce45cf7057b36bb207ec2da01295dc', 0, 0, '2025-05-12T16:25:13.107+0800' )
1747038313183|2|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038313214|6|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (65) AND status = 0)
1747038314827|7|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, 'ecbe08e418d2154a164d384321be1915', 0, 0, '2025-05-12T16:25:14.805+0800' )
1747038314948|4|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (66) AND status = 0)
1747038316138|8|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '28f9774c3028528c7e634365ddd3a932', 0, 0, '2025-05-12T16:25:16.122+0800' )
1747038316679|3|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747038316683|3|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747038331628|2|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038331633|4|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038331782|5|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038331783|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038331786|3|statement|connection 3|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038331789|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:25:31.783+0800')
1747038331789|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:25:31.783+0800')
1747038331793|10|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038331839|7|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038331842|6|statement|connection 3|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038331979|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038331997|16|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 67 AND send_time >= '2025-04-12T16:25:31.979+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038337816|3|statement|connection 3|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'huangjie')
1747038338043|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038338050|4|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'huangjie'              )
1747038338263|5|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038338265|5|statement|connection 3|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 1)
1747038338273|5|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:25:38.264+0800')
1747038338273|6|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038338275|4|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038338278|3|statement|connection 0|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038338290|11|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:25:38.277+0800')
1747038338299|7|statement|connection 4|url *******************************************************  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (?,?,?,?))|SELECT  id,name,owner_id AS ownerId,head_image AS headImage,head_image_thumb AS headImageThumb,notice,deleted,created_time AS createdTime,group_type AS groupType  FROM im_group     WHERE (id IN (2,3,4,5))
1747038338351|4|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747038338360|10|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038338457|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038338475|11|statement|connection 1|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 67 AND send_time >= '2025-04-12T16:25:38.459+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747038338484|12|statement|connection 0|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:25:38.469+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747038338493|6|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:25:38.484+0800')
1747038344099|5|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, 'bbf65ba686ae2d983ec2830604dcd550', 0, 0, '2025-05-12T16:25:44.085+0800' )
1747038344199|4|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038345233|4|statement|connection 4|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038345236|2|commit|connection 4|url **********************:@*************:1521/MIPDW||
1747038369415|8|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '7e2e037baaee175fe33ac30438288f81', 0, 0, '2025-05-12T16:26:09.400+0800' )
1747038372702|5|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '7e2e037baaee175fe33ac30438288f81', 0, 0, '2025-05-12T16:26:12.685+0800' )
1747038375590|5|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 53, '2805d862b37ebc5c88aa20a3a9ea1e76', 0, 0, '2025-05-12T16:26:15.574+0800' )
1747038376323|2|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038376328|5|commit|connection 0|url **********************:@*************:1521/MIPDW||
1747038394267|3|statement|connection 4|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'admin')
1747038394405|2|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038394410|3|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038394440|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038394443|3|statement|connection 0|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038394443|3|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038394445|5|statement|connection 3|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038394446|4|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:26:34.440+0800')
1747038394447|3|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:26:34.443+0800')
1747038394545|2|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038394558|10|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:26:34.547+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038394701|2|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038394712|2|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038394726|3|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038394835|10|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (64,65,57,58,59,60,61,62,63) AND status = 0)
1747038394964|5|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (40) AND status = 0)
1747038397643|4|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038397647|3|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038397670|4|statement|connection 2|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038397674|4|commit|connection 2|url **********************:@*************:1521/MIPDW||
1747038402489|16|statement|connection 3|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, 'e8386acf25aa41a5d006720e31d42320', 0, 0, '2025-05-12T16:26:42.467+0800' )
1747038402530|3|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038404861|18|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, 'e07324494a936780761057bba8c59ce7', 0, 0, '2025-05-12T16:26:44.836+0800' )
1747038406815|6|statement|connection 4|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, 'c368bb4f25b5c929d7d0563e1cc96f5d', 0, 0, '2025-05-12T16:26:46.796+0800' )
1747038413086|3|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=51 
1747038413095|4|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 51 AND status = 3)
1747038416177|5|statement|connection 0|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, '2d5cd5dd4883bedbb4cea2f534a9ca26', 0, 0, '2025-05-12T16:26:56.164+0800' )
1747038418396|5|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, '0ef7b2d36a3b8ba8d9813aa960177e49', 0, 0, '2025-05-12T16:26:58.378+0800' )
1747038420345|5|statement|connection 2|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, '00377ae61ef13df2c0d53a77a77bea52', 0, 0, '2025-05-12T16:27:00.334+0800' )
1747038422802|7|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 1, 51, 'ecbe08e418d2154a164d384321be1915', 0, 0, '2025-05-12T16:27:02.789+0800' )
1747038426180|25|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 1 AND recv_id = 53 AND status = 3)
1747038426183|28|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038426234|3|statement|connection 1|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 53 AND recv_id = 1 AND status = 1)
1747038426238|3|commit|connection 1|url **********************:@*************:1521/MIPDW||
1747038691488|4|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038691494|4|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038691691|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038691697|5|statement|connection 3|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038691698|5|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038691698|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:31:31.692+0800')
1747038691702|10|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038691703|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:31:31.698+0800')
1747038691724|9|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038691734|12|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038691812|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038691823|7|statement|connection 1|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 74 AND send_time >= '2025-04-12T16:31:31.813+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038698670|35|statement|connection 2|url ******************************************************* count(0) FROM im_user|SELECT count(0) FROM im_user
1747038698699|23|statement|connection 2|url ******************************************************* * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user           ORDER BY created_time DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= ? AND PAGEHELPER_ROW_ID > ?|SELECT * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user           ORDER BY created_time DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= 10 AND PAGEHELPER_ROW_ID > 0
1747038699060|23|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              ,                  ?              ,                  ?              ,                  ?              ,                  ?              ,                  ?              ,                  ?              ,                  ?              ,                  ?              ,                  ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'li_yuliang'              ,                  'zou_luncong'              ,                  'yu_chenghao'              ,                  'li_rui'              ,                  'ceshi'              ,                  'shen_xuchao'              ,                  'admin'              ,                  'zou_yunchuan'              ,                  'zhao_xing'              ,                  'zhang_daojian'              )
1747038702708|2|statement|connection 3|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'admin')
1747038702896|12|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038702906|9|statement|connection 4|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038703109|7|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038703120|10|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038703130|18|statement|connection 1|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038703130|18|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:31:43.109+0800')
1747038703130|18|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038703137|14|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:31:43.121+0800')
1747038703137|15|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038703148|7|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038703263|12|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038703276|8|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 74 AND send_time >= '2025-04-12T16:31:43.267+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038710911|14|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038710915|5|statement|connection 4|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038715030|13|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038715037|5|statement|connection 1|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038724449|6|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038724455|5|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038724480|4|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038724488|5|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038724489|6|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038724494|11|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038724498|7|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:32:04.489+0800')
1747038724502|11|statement|connection 4|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:32:04.489+0800')
1747038724571|2|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038724578|6|statement|connection 4|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:32:04.571+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038724624|3|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038724634|6|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (74) AND status = 0)
1747038724646|6|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038724661|4|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038724779|8|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (64,65,66,67) AND status = 0)
1747038724947|7|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (52,53,54,55,56,57,58) AND status = 0)
1747038725068|4|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (35,36) AND status = 0)
1747038739271|2|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038739279|6|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038739648|9|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038739649|3|statement|connection 0|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038739654|6|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038739661|14|statement|connection 4|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038739661|10|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:32:19.649+0800')
1747038739666|6|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038739669|6|statement|connection 3|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038739669|12|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:32:19.655+0800')
1747038739710|8|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747038739714|3|commit|connection 0|url **********************:@*************:1521/MIPDW||
1747038739836|8|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038764952|7|statement|connection 2|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 74 AND send_time >= '2025-04-12T16:32:19.842+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038847401|35|statement|connection 2|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 74 AND send_time >= '2025-04-12T16:32:19.842+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747038897924|162|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038898047|7|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038898514|11|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038898618|14|statement|connection 3|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038898618|15|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038898632|6|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038898637|11|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038898644|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:34:58.619+0800')
1747038898644|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:34:58.634+0800')
1747038898733|10|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038902702|24|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747038902742|36|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747038903373|20|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038903381|29|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038903426|48|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:35:03.373+0800')
1747038903435|49|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747038903458|50|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:35:03.382+0800')
1747038903472|44|statement|connection 0|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747038903472|64|statement|connection 3|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747038903517|42|statement|connection 4|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747038904640|12|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747038904640|24|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747038926187|10|statement|connection 0|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 78 AND send_time >= '2025-04-12T16:35:04.646+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1))) ORDER BY id DESC
1747038926188|11|statement|connection 1|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 74 AND send_time >= '2025-04-12T16:35:04.646+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC
1747038978178|51992|statement|connection 2|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:35:16.796+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747038978189|5|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:36:18.179+0800')
1747038978469|37|statement|connection 1|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 74 AND send_time >= '2025-04-12T16:35:04.646+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC
1747039042221|4|statement|connection 3|url *******************************************************  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = ?)|SELECT  id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds  FROM im_user     WHERE (user_name = 'admin')
1747039042438|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747039042445|4|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747039042761|10|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039042768|3|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039042769|3|statement|connection 4|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747039042769|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:37:22.762+0800')
1747039042776|11|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747039042783|11|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:37:22.769+0800')
1747039042791|9|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039042799|10|statement|connection 1|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747039047006|4053|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039047906|59|statement|connection 2|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 74 AND send_time >= '2025-04-12T16:37:22.955+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC
1747039094593|19|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, 'c368bb4f25b5c929d7d0563e1cc96f5d', 0, 0, '2025-05-12T16:38:14.559+0800' )
1747039096317|9|statement|connection 1|url ******************************************************* INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( ?, ?, ?, ?, ?, ? )|INSERT INTO im_private_message  ( send_id, recv_id, content, type, status, send_time )  VALUES  ( 53, 1, '347c3a0b044bee159c892c7aeffeb54d', 0, 0, '2025-05-12T16:38:16.300+0800' )
1747039104838|3|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747039104847|5|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747039104898|18|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039104899|7|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039104906|8|statement|connection 1|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747039104909|5|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:38:24.898+0800')
1747039104909|6|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:38:24.900+0800')
1747039104917|15|statement|connection 4|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747039105043|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039110556|47|statement|connection 1|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:38:25.044+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC
1747039146490|8|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039146578|18|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (25,26) AND status = 0)
1747039209509|319|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039209511|138|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747039209515|319|statement|connection 1|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 33 AND send_time >= '2025-04-12T16:39:59.974+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC
1747039209666|10|statement|connection 2|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:40:09.631+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747039209688|7|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:40:09.667+0800')
1747039209717|103|statement|connection 3|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 80 AND send_time >= '2025-04-12T16:39:59.974+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1))) ORDER BY id DESC
1747039216243|6|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (34,36) AND status = 0)
1747039216433|8|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (48,49,50,44,45,46,47) AND status = 0)
1747039216599|19|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (59,60,61,62,63) AND status = 0)
1747039216739|5|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (69,70,71,73) AND status = 0)
1747039221789|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747039221832|8|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747039221886|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039221891|4|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039221898|7|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747039221899|6|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:40:21.887+0800')
1747039221902|7|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:40:21.892+0800')
1747039222031|9|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747039224069|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039225105|55|statement|connection 2|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:40:22.166+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC
1747039287685|3|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039287702|4|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039287743|6|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039287766|6|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039287777|5|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (33,25,26,27,28,29,30,31) AND status = 0)
1747039287911|5|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (51,52) AND status = 0)
1747039288086|11|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (66,67,68,69,70,71,72,73,74,79) AND status = 0)
1747039683040|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747039683048|5|statement|connection 3|url *******************************************************  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC|SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 80 AND send_time >= '2025-04-12T16:48:03.036+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1))) ORDER BY id DESC
1747039683056|5|statement|connection 0|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:48:03.047+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747039683061|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:48:03.056+0800')
1747039909537|154|statement|connection 0|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 80 AND send_time >= '2025-04-12T16:51:48.588+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747039909537|139|statement|connection 1|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 80 AND send_time >= '2025-04-12T16:51:48.588+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747039909563|21|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747039909593|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039909656|13|statement|connection 2|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:51:49.620+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747039909672|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:51:49.659+0800')
1747039932661|5|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747039932714|9|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747039933075|6|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039933088|8|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:13.076+0800')
1747039933090|5|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039933093|7|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747039933111|16|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:13.090+0800')
1747039933293|113|statement|connection 1|url *******************************************************   max(id) maxId   FROM im_private_message     WHERE (send_id = ? AND recv_id = ? AND status = ?)|SELECT   max(id) maxId   FROM im_private_message     WHERE (send_id = 53 AND recv_id = 1 AND status = 3)
1747039933294|125|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039933480|54|statement|connection 0|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747039933559|46|statement|connection 2|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (send_id = ? AND recv_id = ? AND status = ?)|UPDATE im_private_message  SET status=3      WHERE (send_id = 1 AND recv_id = 53 AND status = 1)
1747039933576|11|commit|connection 2|url **********************:@*************:1521/MIPDW||
1747039933832|14|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039933867|7|statement|connection 0|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 80 AND send_time >= '2025-04-12T16:52:13.845+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747039938257|4|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747039938268|8|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747039938316|12|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039938338|22|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039938338|22|statement|connection 3|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747039938344|23|statement|connection 1|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:18.317+0800')
1747039938344|28|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747039938358|16|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:18.339+0800')
1747039938445|3|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039938462|5|statement|connection 2|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 0 AND send_time >= '2025-04-12T16:52:18.445+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747039938503|36|statement|connection 2|url ******************************************************* * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= ? AND PAGEHELPER_ROW_ID > ?|SELECT * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:52:18.445+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= 1000 AND PAGEHELPER_ROW_ID > 0
1747039939607|3|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039939723|6|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039939859|5|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (37,38) AND status = 0)
1747039940013|6|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (48,49,50,44,45,46,47) AND status = 0)
1747039940382|5|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (80,79) AND status = 0)
1747039951135|5|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747039951142|4|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747039951177|9|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747039951181|5|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039951181|5|statement|connection 0|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747039951182|5|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039951191|5|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:31.181+0800')
1747039951197|10|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:31.182+0800')
1747039951281|3|statement|connection 2|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039960277|5|statement|connection 0|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 0 AND send_time >= '2025-04-12T16:52:40.100+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747039960298|19|statement|connection 0|url ******************************************************* * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= ? AND PAGEHELPER_ROW_ID > ?|SELECT * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:52:40.100+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= 1000 AND PAGEHELPER_ROW_ID > 0
1747039961130|8|statement|connection 0|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039961176|6|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747039961260|4|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (32,31) AND status = 0)
1747039961463|15|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (40,41,42,43,44) AND status = 0)
1747039961614|3|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (55,56,57,58,59) AND status = 0)
1747039961809|11|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (65,66,67,68) AND status = 0)
1747039975969|40|statement|connection 2|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=53 
1747039975979|5|statement|connection 2|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   ?              )|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'                                                                                   AND SUD.USER_NAME IN              (                   'admin'              )
1747039976043|14|statement|connection 2|url *******************************************************  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = ?)|SELECT  id,user_id AS userId,friend_id AS friendId,friend_nick_name AS friendNickName,friend_head_image AS friendHeadImage,created_time AS createdTime  FROM im_friend     WHERE (user_id = 53)
1747039976050|6|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039976050|5|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039976061|17|statement|connection 1|url ******************************************************* DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               ?          ,              ?          )          AND SUD.SYS_ID = 'IM'|SELECT DISTINCT SUD.DEPT_CODE AS "deptCode",         SUD.GROUP_NAME AS "groupName",         SUD.ORGANIZATION_NAME AS "organizationName",         MD.DEPT_NAME AS "deptName",         SUD.USER_NAME AS "userName",         SU.NICK_NAME AS "nickName",         SU.ID AS "userId",         SU.POST_TITLE AS "postTitle",         SU.sex AS "sex",         SU.head_image_thumb as "headImage",         SU.email AS "email",         SU.post_title AS "postTitle",         SU.mobile AS "mobile"         FROM im_user_dept SUD         LEFT JOIN im_dept MD ON SUD.DEPT_CODE = MD.DEPT_CODE AND MD.DELETED_FLAG = '0'         LEFT JOIN im_user SU ON SU.USER_NAME = SUD.USER_NAME         WHERE         NVL(SU.NICK_NAME, ' ') != ' '         AND NVL(MD.DEPT_NAME, ' ') != ' '         AND SU.role IN          (               '管理员'          ,              '普通用户'          )          AND SUD.SYS_ID = 'IM'
1747039976066|12|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:56.050+0800')
1747039976066|11|statement|connection 3|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = true AND quit_time >= '2025-04-12T16:52:56.050+0800')
1747039984894|8716|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747039990285|17|statement|connection 2|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 0 AND send_time >= '2025-04-12T16:53:05.563+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747039990418|69|statement|connection 2|url ******************************************************* * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= ? AND PAGEHELPER_ROW_ID > ?|SELECT * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 0 AND send_time >= '2025-04-12T16:53:05.563+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= 1000 AND PAGEHELPER_ROW_ID > 0
1747040143068|4|statement|connection 3|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747040143100|6|statement|connection 1|url ******************************************************* id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=? |SELECT id,user_name AS userName,nick_name AS nickName,sex,head_image AS headImage,head_image_thumb AS headImageThumb,type,signature,password,last_login_time AS lastLoginTime,created_time AS createdTime,mobile,email,post_title AS postTitle,expire_days AS expireDays,role,chat_object_ids AS chatObjectIds FROM im_user WHERE id=1 
1747040143205|9|statement|connection 0|url **********************:@*************:1521/MIPDW|UPDATE im_private_message  SET status=?      WHERE (id IN (?,?,?,?,?) AND status = ?)|UPDATE im_private_message  SET status=1      WHERE (id IN (32,28,29,30,31) AND status = 0)
1747040201595|168|statement|connection 1|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 35 AND send_time >= '2025-04-12T16:56:40.238+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53)))
1747040201601|190|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 53 AND quit = false)
1747040201651|12|statement|connection 1|url ******************************************************* * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= ? AND PAGEHELPER_ROW_ID > ?|SELECT * FROM (  SELECT TMP_PAGE.*, ROWNUM PAGEHELPER_ROW_ID FROM (  SELECT  id,send_id AS sendId,recv_id AS recvId,content,type,status,send_time AS sendTime  FROM im_private_message     WHERE (id > 35 AND send_time >= '2025-04-12T16:56:40.238+0800' AND status <> 2 AND ((send_id = 53) OR (recv_id = 53))) ORDER BY id DESC  ) TMP_PAGE) WHERE PAGEHELPER_ROW_ID <= 10 AND PAGEHELPER_ROW_ID > 0
1747040231431|9|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = false)
1747040231442|3|statement|connection 0|url ******************************************************* count(0) FROM im_private_message WHERE (id > ? AND send_time >= ? AND status <> ? AND ((send_id = ?) OR (recv_id = ?)))|SELECT count(0) FROM im_private_message WHERE (id > 80 AND send_time >= '2025-04-12T16:57:11.421+0800' AND status <> 2 AND ((send_id = 1) OR (recv_id = 1)))
1747040231492|17|statement|connection 0|url *******************************************************  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > ? AND send_time > ? AND group_id IN (?,?,?,?) AND status <> ?)|SELECT  id,group_id AS groupId,send_id AS sendId,send_nick_name AS sendNickName,recv_ids AS recvIds,at_user_ids AS atUserIds,content,type,receipt,receipt_ok AS receiptOk,status,send_time AS sendTime  FROM im_group_message     WHERE (id > 4 AND send_time > '2025-04-12T16:57:11.452+0800' AND group_id IN (2,3,4,5) AND status <> 2)
1747040231509|4|statement|connection 0|url *******************************************************  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = ? AND quit = ? AND quit_time >= ?)|SELECT  id,group_id AS groupId,user_id AS userId,alias_name AS aliasName,head_image AS headImage,remark,quit,quit_time AS quitTime,created_time AS createdTime  FROM im_group_member     WHERE (user_id = 1 AND quit = true AND quit_time >= '2025-04-12T16:57:11.495+0800')
