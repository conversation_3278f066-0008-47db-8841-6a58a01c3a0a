server:
  port: 8889
spring:
  application:
    name: im-platform
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    url: ************************************************
    username: mip
    password: Jykj1994

  redis:
    host: *************
    port: 6379

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

mybatis-plus:
  configuration:
    # 是否开启自动驼峰命名规则
    map-underscore-to-camel-case: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations:
    - classpath*:mapper/oracle/*.xml
minio:
  endpoint: http://*************:9001 #内网地址
  public: https://***************:9100/file  #外网访问地址
  accessKey: admin
  secretKey: Jykj1994@
  bucketName: box-im
  imagePath: image
  filePath: file
  videoPath: video

webrtc:
  iceServers:
    - urls: stun:stun.l.google.com:19302

jwt:
  accessToken:
    expireIn: 1800 #半个小时
    secret: MIIBIjANBgkq
  refreshToken:
    expireIn: 604800 #7天
    secret: IKDiqVmn0VFU

im:
  config:
    file:
      clean: true #是否定时清理文件
      #清理90天前的文件
      clean-days: 90

