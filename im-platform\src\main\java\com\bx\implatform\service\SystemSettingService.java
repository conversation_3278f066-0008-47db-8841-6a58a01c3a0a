package com.bx.implatform.service;

import com.bx.implatform.entity.SysConfig;
import com.bx.implatform.result.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 系统设置
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 13:48
 */
public interface SystemSettingService {
    /**
     * 获取系统配置
     *
     * @return Result
     * <AUTHOR>
     */
    Result querySysConfig();

    /**
     * 添加系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    Result addSysConfig(List<SysConfig> sysConfigList);

    /**
     * 更新系统设置
     *
     * @param sysConfigList List<SysConfig>
     * @return Result
     * <AUTHOR>
     */
    Result updateSysConfigs(List<SysConfig> sysConfigList);

    /**
     * 获取所有系统设置（包括webShow为N和Y的）
     *
     * @return Map<String, Object>
     * <AUTHOR>
     */
    Result queryAllSysConfig();

    /**
     * 获取系统设置（sys_config中MODULE_ID=1）
     *
     * @return 系统设置
     * <AUTHOR>
     */
    Result queryCommonSysConfig();
}