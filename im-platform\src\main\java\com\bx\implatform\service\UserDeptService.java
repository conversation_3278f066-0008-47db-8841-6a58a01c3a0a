package com.bx.implatform.service;

import com.bx.implatform.dto.UserDeptDTO;
import com.bx.implatform.entity.UserDept;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bx.implatform.result.Result;

/**
* <AUTHOR>
* @description 针对表【im_user_dept(用户部门关系表)】的数据库操作Service
* @createDate 2024-05-21 15:19:27
*/
public interface UserDeptService extends IService<UserDept> {

    Result getOrganizationalStructure(UserDeptDTO userDeptDTO);

    Result getOnlyOrganizationalStructure(UserDeptDTO userDeptDTO);
}
