package com.bx.implatform.util;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 针对PageInfo封装的类型转换类
 *
 * <AUTHOR>
 */
@Slf4j
public class PageUtil {
    /**
     * 通用pageInfo转换（针对属性名称不相同的）
     *
     * @param sourcePageInfo 源数据
     * @param mapper         list转换方法
     * @param <T>            目标类型
     * @param <S>            源类型
     * @return PageInfo<T>
     */
    public static <T, S> PageInfo<T> pageInfoCopy(PageInfo<S> sourcePageInfo, Function<S, T> mapper) {
        PageInfo<T> respPageInfo = setBasePageInfo(sourcePageInfo);
        List<T> pageList = sourcePageInfo.getList().stream().map(mapper).collect(Collectors.toList());
        respPageInfo.setList(pageList);
        return respPageInfo;
    }

    /**
     * 通用pageInfo转换（针对属性名称相同的简化方法）
     *
     * @param sourcePageInfo 源数据
     * @param targetClass    目标类型
     * @param <T>            目标类型
     * @param <S>            源类型
     * @return PageInfo<T>
     * <AUTHOR>
     */
    public static <T, S> PageInfo<T> pageInfoCopy(PageInfo<S> sourcePageInfo, Class<T> targetClass) {
        PageInfo<T> respPageInfo = setBasePageInfo(sourcePageInfo);
        List<T> pageList = MapperUtils.INSTANCE.mapAsList(targetClass, sourcePageInfo.getList());
        respPageInfo.setList(pageList);
        return respPageInfo;
    }

    /**
     * 设置基础信息，不包括List
     *
     * @param sourcePageInfo 源数据
     * @param <T>            目标类型
     * @param <S>            源类型
     * @return PageInfo<T>
     */
    private static <T, S> PageInfo<T> setBasePageInfo(PageInfo<S> sourcePageInfo) {
        PageInfo<T> respPageInfo = new PageInfo<>();
        respPageInfo.setPageNum(sourcePageInfo.getPageNum());
        respPageInfo.setPageSize(sourcePageInfo.getPageSize());
        respPageInfo.setSize(sourcePageInfo.getSize());
        respPageInfo.setStartRow(sourcePageInfo.getStartRow());
        respPageInfo.setEndRow(sourcePageInfo.getEndRow());
        respPageInfo.setPages(sourcePageInfo.getPages());
        respPageInfo.setPrePage(sourcePageInfo.getPrePage());
        respPageInfo.setNextPage(sourcePageInfo.getNextPage());
        respPageInfo.setIsFirstPage(sourcePageInfo.isIsFirstPage());
        respPageInfo.setIsLastPage(sourcePageInfo.isIsLastPage());
        respPageInfo.setHasPreviousPage(sourcePageInfo.isHasPreviousPage());
        respPageInfo.setHasNextPage(sourcePageInfo.isHasNextPage());
        respPageInfo.setNavigatePages(sourcePageInfo.getNavigatePages());
        respPageInfo.setNavigatepageNums(sourcePageInfo.getNavigatepageNums());
        respPageInfo.setNavigateFirstPage(sourcePageInfo.getNavigateFirstPage());
        respPageInfo.setNavigateLastPage(sourcePageInfo.getNavigateLastPage());
        respPageInfo.setTotal(sourcePageInfo.getTotal());
        return respPageInfo;
    }
}
