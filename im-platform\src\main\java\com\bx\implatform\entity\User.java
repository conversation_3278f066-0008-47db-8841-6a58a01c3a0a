package com.bx.implatform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("im_user")
public class User extends Model<User> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户名
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 性别
     */
    @TableField("sex")
    private Integer sex;

    /**
     * 头像
     */
    @TableField("head_image")
    private String headImage;

    /**
     * 头像缩略图
     */
    @TableField("head_image_thumb")
    private String headImageThumb;

    /**
     * 用户类型  1:普通用户 2:审核专用账户
     */
    @TableField("type")
    private Integer type;

    /**
     * 个性签名
     */
    @TableField("signature")
    private String signature;
    /**
     * 密码(明文)
     */
    @TableField("password")
    private String password;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private Date lastLoginTime;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 职位
     */
    @TableField("post_title")
    private String postTitle;

    /**
     * 过期时间，天数，针对临时用户
     */
    @ApiModelProperty(value = "过期时间，天数，针对临时用户")
    @TableField("expire_days")
    private Long expireDays;;

    /**
     * 用户角色，固定三种角色：管理员、普通用户、临时用户
     */
    @NotBlank(message = "用户角色不可为空")
    @ApiModelProperty(value = "用户角色，固定三种角色：管理员、普通用户、临时用户")
    @TableField("role")
    private String role = "普通用户";

    /**
     * 临时用户聊天对象Id
     */
    @ApiModelProperty(value = "临时用户聊天对象Id")
    @TableField("chat_object_ids")
    private String chatObjectIds;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
