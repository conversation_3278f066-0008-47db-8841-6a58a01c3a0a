<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bx.implatform.mapper.SysConfigMapper">
    <resultMap id="BaseResultMap" type="com.bx.implatform.entity.SysConfig">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="MODULE_ID" jdbcType="VARCHAR" property="moduleId"/>
        <result column="MODULE_NAME" jdbcType="VARCHAR" property="moduleName"/>
        <result column="CONFIG_CODE" jdbcType="VARCHAR" property="configCode"/>
        <result column="CONFIG_NAME" jdbcType="VARCHAR" property="configName"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CONFIG_VALUE" jdbcType="VARCHAR" property="configValue"/>
        <result column="CONFIG_TYPE" jdbcType="CHAR" property="configType"/>
        <result column="SORT_CODE" jdbcType="INTEGER" property="sortCode"/>
        <result column="DELETED_FLAG" jdbcType="CHAR" property="deletedFlag"/>
        <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="SYS_ID" jdbcType="VARCHAR" property="sysId"/>
        <result column="WEB_SHOW" jdbcType="CHAR" property="webShow"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, MODULE_ID, MODULE_NAME, CONFIG_CODE, CONFIG_NAME, DESCRIPTION, CONFIG_VALUE, CONFIG_TYPE,
    SORT_CODE, DELETED_FLAG, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME, SYS_ID, 
    WEB_SHOW
    </sql>
    <select id="querySysConfig" resultType="com.bx.implatform.entity.SysConfig">
        select
        <include refid="Base_Column_List"/>
        from SYS_CONFIG
        where DELETED_FLAG=0 AND SYS_ID='SSO' order by MODULE_ID,SORT_CODE ASC
    </select>

    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.bx.implatform.entity.SysConfig" useGeneratedKeys="true">
        insert into SYS_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="moduleId != null">
                MODULE_ID,
            </if>
            <if test="moduleName != null">
                MODULE_NAME,
            </if>
            <if test="configCode != null">
                CONFIG_CODE,
            </if>
            <if test="configName != null">
                CONFIG_NAME,
            </if>
            <if test="description != null">
                DESCRIPTION,
            </if>
            <if test="configValue != null">
                CONFIG_VALUE,
            </if>
            <if test="configType != null">
                CONFIG_TYPE,
            </if>
            <if test="sortCode != null">
                SORT_CODE,
            </if>
            <if test="deletedFlag != null">
                DELETED_FLAG,
            </if>
            <if test="createBy != null">
                CREATE_BY,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateBy != null">
                UPDATE_BY,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="sysId != null">
                SYS_ID,
            </if>
            <if test="webShow != null">
                WEB_SHOW,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="moduleId != null">
                #{moduleId,jdbcType=VARCHAR},
            </if>
            <if test="moduleName != null">
                #{moduleName,jdbcType=VARCHAR},
            </if>
            <if test="configCode != null">
                #{configCode,jdbcType=VARCHAR},
            </if>
            <if test="configName != null">
                #{configName,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null">
                #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="configType != null">
                #{configType,jdbcType=CHAR},
            </if>
            <if test="sortCode != null">
                #{sortCode,jdbcType=INTEGER},
            </if>
            <if test="deletedFlag != null">
                #{deletedFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sysId != null">
                #{sysId,jdbcType=VARCHAR},
            </if>
            <if test="webShow != null">
                #{webShow,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.bx.implatform.entity.SysConfig">
        update SYS_CONFIG
        <set>
            <if test="moduleId != null">
                MODULE_ID = #{moduleId,jdbcType=VARCHAR},
            </if>
            <if test="moduleName != null">
                MODULE_NAME = #{moduleName,jdbcType=VARCHAR},
            </if>
            <if test="configCode != null">
                CONFIG_CODE = #{configCode,jdbcType=VARCHAR},
            </if>
            <if test="configName != null">
                CONFIG_NAME = #{configName,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null">
                CONFIG_VALUE = #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="configType != null">
                CONFIG_TYPE = #{configType,jdbcType=CHAR},
            </if>
            <if test="sortCode != null">
                SORT_CODE = #{sortCode,jdbcType=INTEGER},
            </if>
            <if test="deletedFlag != null">
                DELETED_FLAG = #{deletedFlag,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                CREATE_BY = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sysId != null">
                SYS_ID = #{sysId,jdbcType=VARCHAR},
            </if>
            <if test="webShow != null">
                WEB_SHOW = #{webShow,jdbcType=CHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER} AND SYS_ID='SSO'
    </update>

    <select id="querySysConfigAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SYS_CONFIG
        where DELETED_FLAG=0 AND SYS_ID ='SSO' order by MODULE_ID,SORT_CODE ASC
    </select>
</mapper>