/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : Oracle
 Source Server Version : 190000
 Source Host           : *************:1521
 Source Schema         : MIP

 Target Server Type    : Oracle
 Target Server Version : 190000
 File Encoding         : 65001

 Date: 24/04/2025 17:54:38
*/


-- ----------------------------
-- Table structure for IM_DEPT
-- ----------------------------
DROP TABLE "MIP"."IM_DEPT";
CREATE TABLE "MIP"."IM_DEPT" (
  "DEPT_CODE" NVARCHAR2(16) VISIBLE NOT NULL,
  "DEPT_NAME" NVARCHAR2(64) VISIBLE,
  "DELETED_FLAG" NCHAR(1) VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_DEPT"."DEPT_CODE" IS '部门编码';
COMMENT ON COLUMN "MIP"."IM_DEPT"."DEPT_NAME" IS '部门名称';
COMMENT ON COLUMN "MIP"."IM_DEPT"."DELETED_FLAG" IS '删除标志0：未删除，1删除';

-- ----------------------------
-- Records of IM_DEPT
-- ----------------------------
INSERT INTO "MIP"."IM_DEPT" VALUES ('JGSYB', '军工事业部', '0');
INSERT INTO "MIP"."IM_DEPT" VALUES ('YLSYB', '医疗事业部', '0');

-- ----------------------------
-- Table structure for IM_FRIEND
-- ----------------------------
DROP TABLE "MIP"."IM_FRIEND";
CREATE TABLE "MIP"."IM_FRIEND" (
  "ID" NUMBER(20,0) VISIBLE NOT NULL,
  "USER_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "FRIEND_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "FRIEND_NICK_NAME" NVARCHAR2(255) VISIBLE NOT NULL,
  "FRIEND_HEAD_IMAGE" NVARCHAR2(255) VISIBLE,
  "CREATED_TIME" DATE VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_FRIEND"."ID" IS 'id';
COMMENT ON COLUMN "MIP"."IM_FRIEND"."USER_ID" IS '用户id';
COMMENT ON COLUMN "MIP"."IM_FRIEND"."FRIEND_ID" IS '好友id';
COMMENT ON COLUMN "MIP"."IM_FRIEND"."FRIEND_NICK_NAME" IS '好友昵称';
COMMENT ON COLUMN "MIP"."IM_FRIEND"."FRIEND_HEAD_IMAGE" IS '好友头像';
COMMENT ON COLUMN "MIP"."IM_FRIEND"."CREATED_TIME" IS '创建时间';
COMMENT ON TABLE "MIP"."IM_FRIEND" IS '好友';

-- ----------------------------
-- Records of IM_FRIEND
-- ----------------------------

-- ----------------------------
-- Table structure for IM_GROUP
-- ----------------------------
DROP TABLE "MIP"."IM_GROUP";
CREATE TABLE "MIP"."IM_GROUP" (
  "ID" NUMBER(20,0) VISIBLE NOT NULL,
  "NAME" NVARCHAR2(255) VISIBLE NOT NULL,
  "OWNER_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "HEAD_IMAGE" NVARCHAR2(255) VISIBLE,
  "HEAD_IMAGE_THUMB" NVARCHAR2(255) VISIBLE,
  "NOTICE" NCLOB VISIBLE,
  "REMARK" NVARCHAR2(255) VISIBLE,
  "DELETED" NUMBER(4,0) VISIBLE,
  "CREATED_TIME" DATE VISIBLE,
  "GROUP_TYPE" NUMBER(4,0) VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_GROUP"."ID" IS 'id';
COMMENT ON COLUMN "MIP"."IM_GROUP"."NAME" IS '群名字';
COMMENT ON COLUMN "MIP"."IM_GROUP"."OWNER_ID" IS '群主id';
COMMENT ON COLUMN "MIP"."IM_GROUP"."HEAD_IMAGE" IS '群头像';
COMMENT ON COLUMN "MIP"."IM_GROUP"."HEAD_IMAGE_THUMB" IS '群头像缩略图';
COMMENT ON COLUMN "MIP"."IM_GROUP"."NOTICE" IS '群公告';
COMMENT ON COLUMN "MIP"."IM_GROUP"."REMARK" IS '群备注';
COMMENT ON COLUMN "MIP"."IM_GROUP"."DELETED" IS '是否已删除';
COMMENT ON COLUMN "MIP"."IM_GROUP"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "MIP"."IM_GROUP"."GROUP_TYPE" IS '群聊类型：0：默认群聊(无法删除)；1：用户创建';
COMMENT ON TABLE "MIP"."IM_GROUP" IS '群';

-- ----------------------------
-- Records of IM_GROUP
-- ----------------------------
INSERT INTO "MIP"."IM_GROUP" VALUES ('54', '医疗事业部', '41', NULL, NULL, NULL, NULL, '0', TO_DATE('2024-11-08 01:55:48', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('55', '123', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-18 06:44:44', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('56', '1111', '1', NULL, NULL, '11111', NULL, '1', TO_DATE('2024-11-18 08:09:50', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('57', '22222', '1', 'https://***************:9100/file/box-im/image/20241119/1731982574778.png', NULL, '22222', NULL, '1', TO_DATE('2024-11-19 02:17:08', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('58', '23234', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-19 06:50:58', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('59', '聊天吧', '45', NULL, NULL, NULL, NULL, '0', TO_DATE('2024-11-20 02:12:59', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('60', '54444', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 02:19:21', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('61', '11111', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 02:28:47', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('62', '111', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 02:32:27', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('63', '12312', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 02:33:46', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('64', '123123123', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 02:35:11', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('65', '123123', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 02:38:10', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('66', '123123', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 02:38:23', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('67', 'aaa', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 09:20:07', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('68', 'bbb', '45', NULL, NULL, '131313', NULL, '1', TO_DATE('2024-11-20 09:39:57', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('69', '11111', '1', NULL, NULL, '11111111', NULL, '1', TO_DATE('2024-11-20 09:41:06', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('70', '111112', '1', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 09:51:16', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('71', '123123', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 09:52:36', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('72', '1111121111', '1', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 17:53:44', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('73', '1111', '1', 'https://***************:9100/file/box-im/image/20241120/1732096602032.png', NULL, '11111111233', NULL, '0', TO_DATE('2024-11-20 17:56:49', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('74', '测试', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-20 17:58:22', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('75', '测试群2', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-21 09:37:47', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('76', '测试群3', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-21 09:50:13', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('77', '12312', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-21 09:53:54', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('78', '4565456', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-21 09:57:01', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('79', '测试群1', '45', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-11-21 10:04:07', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('80', '测试群聊', '1', 'https://***************:9100/file/box-im/image/20241125/1732504141434.png', NULL, '测试群聊公告', NULL, '0', TO_DATE('2024-11-25 11:09:29', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('81', '测试新建群2', '1', NULL, NULL, '111111', NULL, '0', TO_DATE('2024-11-25 11:37:05', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('82', '1', '42', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-12-03 14:15:49', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('83', '123', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-12-03 15:28:54', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('84', '456', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-12-03 15:29:14', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('85', '556', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-12-03 15:30:33', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('86', '123123', '51', NULL, NULL, NULL, NULL, '0', TO_DATE('2024-12-03 15:33:24', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('87', '测试aaa', '51', NULL, NULL, NULL, NULL, '0', TO_DATE('2024-12-03 15:37:48', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('88', '测试4444', '51', NULL, NULL, NULL, NULL, '0', TO_DATE('2024-12-03 15:43:38', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('89', '34567', '51', NULL, NULL, NULL, NULL, '0', TO_DATE('2024-12-03 15:50:59', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('90', '测试222', '51', NULL, NULL, NULL, NULL, '0', TO_DATE('2024-12-03 16:00:30', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('91', '测试小群', '47', NULL, NULL, '测试', NULL, '1', TO_DATE('2024-12-03 17:28:43', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('92', '测试群555', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-12-04 10:17:58', 'SYYYY-MM-DD HH24:MI:SS'), '1');
INSERT INTO "MIP"."IM_GROUP" VALUES ('93', '测试9991', '51', NULL, NULL, NULL, NULL, '1', TO_DATE('2024-12-05 10:18:06', 'SYYYY-MM-DD HH24:MI:SS'), '1');

-- ----------------------------
-- Table structure for IM_GROUP_MEMBER
-- ----------------------------
DROP TABLE "MIP"."IM_GROUP_MEMBER";
CREATE TABLE "MIP"."IM_GROUP_MEMBER" (
  "ID" NUMBER(20,0) VISIBLE NOT NULL,
  "GROUP_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "USER_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "ALIAS_NAME" NVARCHAR2(255) VISIBLE,
  "HEAD_IMAGE" NVARCHAR2(255) VISIBLE,
  "REMARK" NVARCHAR2(255) VISIBLE,
  "QUIT" NUMBER(4,0) VISIBLE,
  "QUIT_TIME" DATE VISIBLE,
  "CREATED_TIME" DATE VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."ID" IS 'id';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."GROUP_ID" IS '群id';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."USER_ID" IS '用户id';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."ALIAS_NAME" IS '组内显示名称';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."HEAD_IMAGE" IS '用户头像';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."REMARK" IS '备注';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."QUIT" IS '是否已退出';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."QUIT_TIME" IS '退出时间';
COMMENT ON COLUMN "MIP"."IM_GROUP_MEMBER"."CREATED_TIME" IS '创建时间';
COMMENT ON TABLE "MIP"."IM_GROUP_MEMBER" IS '群成员';

-- ----------------------------
-- Records of IM_GROUP_MEMBER
-- ----------------------------
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('180', '78', '45', '谢宇', NULL, '4565456', '1', TO_DATE('2024-11-21 10:02:36', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 01:57:01', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('181', '78', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '4565456', '1', TO_DATE('2024-11-21 10:02:36', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 09:57:02', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('182', '79', '45', '谢宇', NULL, '测试群1224766', '1', TO_DATE('2024-11-21 14:23:28', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 02:04:06', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('183', '79', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试群1', '1', TO_DATE('2024-11-21 14:23:28', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 10:04:07', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('184', '56', '33', '何平', NULL, '1111', '1', TO_DATE('2024-11-25 11:18:09', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-22 10:38:36', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('185', '80', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '测试群聊', '0', NULL, TO_DATE('2024-11-25 03:09:29', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('186', '80', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试群聊', '0', NULL, TO_DATE('2024-11-25 11:09:29', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('187', '80', '53', '管理员', NULL, '测试群聊', '0', NULL, TO_DATE('2024-11-25 11:09:29', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('188', '81', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '测试新建群2', '0', NULL, TO_DATE('2024-11-25 03:37:04', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('189', '81', '33', '何平', 'https://***************:9100/file/box-im/image/20241127/1732695558686.jpg', '测试新建群2', '0', NULL, TO_DATE('2024-11-25 11:37:05', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('190', '81', '53', '管理员', NULL, '测试新建群2', '0', NULL, TO_DATE('2024-11-25 11:37:05', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('191', '82', '42', '卫新元', 'https://***************:9100/file/box-im/image/20241108/1731031324737.jpg', '1', '1', TO_DATE('2024-12-03 17:47:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 06:15:49', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('192', '82', '33', '何平', 'https://***************:9100/file/box-im/image/20241127/1732695558686.jpg', '1', '1', TO_DATE('2024-12-03 17:47:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 14:19:27', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('193', '83', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '123', '1', TO_DATE('2024-12-03 15:30:06', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 07:28:54', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('194', '83', '45', '谢宇', NULL, '123', '1', TO_DATE('2024-12-03 15:30:06', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 15:28:54', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('195', '84', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '456', '1', TO_DATE('2024-12-03 15:33:48', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 07:29:13', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('196', '84', '45', '谢宇', NULL, '456', '1', TO_DATE('2024-12-03 15:33:48', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 15:29:14', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('197', '85', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '556', '1', TO_DATE('2024-12-03 15:33:52', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 07:30:33', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('198', '85', '45', '谢宇', NULL, '556', '1', TO_DATE('2024-12-03 15:33:52', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 15:30:33', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('199', '85', '53', '管理员', NULL, '556', '1', TO_DATE('2024-12-03 15:33:52', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 15:30:33', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('200', '86', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '123123', '0', NULL, TO_DATE('2024-12-03 07:33:23', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('201', '86', '45', '谢宇', NULL, '123123', '0', NULL, TO_DATE('2024-12-03 15:33:25', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('202', '87', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试aaa', '0', NULL, TO_DATE('2024-12-03 07:37:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('203', '87', '45', '谢宇', NULL, '测试aaa', '0', NULL, TO_DATE('2024-12-03 15:37:49', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('204', '88', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试4444', '0', NULL, TO_DATE('2024-12-03 07:43:37', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('205', '88', '45', '谢宇', NULL, '测试4444', '0', NULL, TO_DATE('2024-12-03 15:43:38', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('206', '89', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '34567', '0', NULL, TO_DATE('2024-12-03 07:50:59', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('207', '89', '45', '谢宇', NULL, '34567', '0', NULL, TO_DATE('2024-12-03 15:51:00', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('208', '90', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试222', '0', NULL, TO_DATE('2024-12-03 08:00:29', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('209', '90', '45', '谢宇', NULL, '测试222', '0', NULL, TO_DATE('2024-12-03 16:00:30', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('210', '91', '47', '胥珊', 'https://***************:9100/file/box-im/image/20241203/1733212715427.png', '测试小群1', '1', TO_DATE('2024-12-03 17:29:49', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 09:28:42', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('211', '91', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '测试小群', '1', TO_DATE('2024-12-03 17:29:49', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 17:29:34', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('212', '91', '53', '管理员', NULL, '测试小群', '1', TO_DATE('2024-12-03 17:29:49', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 17:29:34', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('213', '91', '32', '豆易', NULL, '测试小群', '1', TO_DATE('2024-12-03 17:29:49', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-03 17:29:34', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('214', '54', '56', '李锐', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('215', '92', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试群555', '1', TO_DATE('2024-12-05 17:26:10', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-04 02:17:57', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('216', '92', '45', '谢宇', NULL, '测试群555', '1', TO_DATE('2024-12-05 17:26:10', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-04 10:17:59', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('217', '92', '53', '管理员', NULL, '测试群555', '1', TO_DATE('2024-12-05 17:26:10', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-04 10:17:59', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('218', '90', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '测试222', '1', TO_DATE('2024-12-20 11:13:52', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-04 14:27:21', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('219', '90', '53', '管理员', NULL, '测试222', '1', TO_DATE('2024-12-20 11:13:54', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-04 14:27:21', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('220', '93', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试888', '1', TO_DATE('2024-12-05 17:26:05', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-05 02:18:05', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('221', '93', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '测试888', '1', TO_DATE('2024-12-05 17:26:05', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-05 10:18:06', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('222', '93', '45', '谢宇', NULL, '测试888', '1', TO_DATE('2024-12-05 17:26:05', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-05 10:18:06', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('223', '93', '53', '管理员', NULL, '测试888', '1', TO_DATE('2024-12-05 17:26:05', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-05 10:18:06', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('224', '93', '42', '卫新元', 'https://***************:9100/file/box-im/image/20241108/1731031324737.jpg', '测试888', '1', TO_DATE('2024-12-05 17:26:05', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-12-05 10:19:59', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('108', '54', '41', '王星', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-11-08 09:56:31', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('109', '54', '33', '何平', 'https://***************:9100/file/box-im/image/20241127/1732695558686.jpg', '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('110', '54', '34', '胡晶玲', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('111', '54', '35', '姜腾德', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('112', '54', '36', '李建奎', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('113', '54', '37', '李蕊伶', 'https://***************:9100/file/box-im/image/20241108/1731031402633.png', '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('114', '54', '38', '李欣', 'https://***************:9100/file/box-im/image/20241108/1731031323138.png', '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('115', '54', '39', '孙杨', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('116', '54', '40', '谭军', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('117', '54', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('118', '54', '30', '陈俊', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('119', '54', '32', '豆易', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('120', '54', '42', '卫新元', 'https://***************:9100/file/box-im/image/20241108/1731031324737.jpg', '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('121', '54', '43', '伍枫飏', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('122', '54', '44', '骁釜', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('123', '54', '45', '谢宇', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('124', '54', '46', '徐苗', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('125', '54', '47', '胥珊', 'https://***************:9100/file/box-im/image/20241203/1733212715427.png', '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('126', '54', '48', '杨迥', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('127', '54', '49', '杨志强', NULL, '医疗事业部', '1', TO_DATE('2024-11-21 16:25:13', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-08 09:56:31', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('128', '54', '50', '张道见', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('129', '54', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('130', '54', '52', '邹云川', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('131', '54', '54', '沈序超', NULL, '医疗事业部', '0', NULL, TO_DATE('2024-12-03 17:39:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('132', '55', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '123', '1', TO_DATE('2024-11-19 10:37:47', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-18 06:44:44', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('133', '55', '45', '谢宇', NULL, '123', '1', TO_DATE('2024-11-19 10:37:47', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-18 14:44:46', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('134', '56', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '1111', '1', TO_DATE('2024-11-25 11:18:09', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-18 08:09:50', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('135', '56', '53', '管理员', NULL, '1111', '1', TO_DATE('2024-11-25 11:18:09', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-22 10:38:36', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('136', '57', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 02:17:08', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('137', '57', '33', '何平', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:17:09', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('138', '57', '35', '姜腾德', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:17:09', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('139', '57', '36', '李建奎', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:17:09', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('140', '57', '39', '孙杨', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:17:09', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('141', '57', '40', '谭军', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:17:09', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('142', '57', '41', '王星', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:17:09', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('143', '57', '43', '伍枫飏', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:17:09', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('144', '57', '53', '管理员', NULL, '22222', '1', TO_DATE('2024-11-25 11:18:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 10:19:51', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('145', '58', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '23234', '1', TO_DATE('2024-11-20 10:19:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 06:50:58', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('146', '58', '45', '谢宇', NULL, '23234', '1', TO_DATE('2024-11-20 10:19:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 17:03:53', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('147', '58', '53', '管理员', NULL, '23234', '1', TO_DATE('2024-11-20 10:19:12', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-19 17:03:53', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('148', '59', '45', '谢宇123', NULL, '聊天吧', '0', NULL, TO_DATE('2024-11-20 02:12:59', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('149', '59', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '聊天吧', '0', NULL, TO_DATE('2024-11-20 10:13:00', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('150', '60', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '54444', '1', TO_DATE('2024-11-20 10:32:01', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 02:19:21', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('151', '60', '45', '谢宇', NULL, '54444', '1', TO_DATE('2024-11-20 10:32:01', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 10:19:23', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('152', '61', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '11111', '1', TO_DATE('2024-11-20 10:32:04', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 02:28:47', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('153', '61', '45', '谢宇', NULL, '11111', '1', TO_DATE('2024-11-20 10:32:04', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 10:28:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('154', '62', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '111', '1', TO_DATE('2024-11-20 10:39:46', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 02:32:27', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('155', '62', '45', '谢宇', NULL, '111', '1', TO_DATE('2024-11-20 10:39:46', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 10:32:28', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('156', '63', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '12312', '1', TO_DATE('2024-11-20 10:39:50', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 02:33:46', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('157', '63', '39', '孙杨', NULL, '12312', '1', TO_DATE('2024-11-20 10:39:50', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 10:33:47', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('158', '64', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '123123123', '1', TO_DATE('2024-11-20 10:39:53', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 02:35:11', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('159', '65', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '123123', '1', TO_DATE('2024-11-20 10:40:11', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 02:38:10', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('160', '65', '39', '孙杨', NULL, '123123', '1', TO_DATE('2024-11-20 10:40:11', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 10:38:13', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('161', '66', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '123123', '1', TO_DATE('2024-11-20 10:40:14', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 02:38:23', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('162', '66', '39', '孙杨', NULL, '123123', '1', TO_DATE('2024-11-20 10:40:14', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 10:38:25', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('163', '67', '45', '谢宇', NULL, 'aaa', '1', TO_DATE('2024-11-21 10:03:01', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 09:20:07', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('164', '67', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', 'aaa', '1', TO_DATE('2024-11-21 10:03:01', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 17:20:08', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('165', '68', '45', '谢宇', NULL, 'bbb', '1', TO_DATE('2024-11-21 10:02:58', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 17:39:59', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('166', '69', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '11111', '1', TO_DATE('2024-11-25 11:18:16', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 09:41:06', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('167', '69', '33', '何平', NULL, '11111', '1', TO_DATE('2024-11-25 11:18:16', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 17:41:07', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('168', '69', '35', '姜腾德', NULL, '11111', '1', TO_DATE('2024-11-25 11:18:16', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 17:41:07', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('169', '70', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '1111122', '1', TO_DATE('2024-11-25 11:18:19', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 09:51:59', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('170', '71', '45', '谢宇', NULL, '123123', '1', TO_DATE('2024-11-21 10:02:55', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 17:52:37', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('171', '72', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '1111122', '1', TO_DATE('2024-11-25 11:18:21', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 09:53:47', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('172', '73', '1', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '1111', '0', NULL, TO_DATE('2024-11-20 09:56:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('173', '73', '33', '何平', 'https://***************:9100/file/box-im/image/20241127/1732695558686.jpg', '1111', '0', NULL, TO_DATE('2024-11-20 17:56:49', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('174', '73', '35', '姜腾德', NULL, '1111', '0', NULL, TO_DATE('2024-11-20 17:56:49', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('175', '74', '45', '谢宇', NULL, '测试', '1', TO_DATE('2024-11-21 10:02:50', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-20 17:58:22', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('176', '75', '45', '谢宇', NULL, '测试群2', '1', TO_DATE('2024-11-21 10:02:47', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 09:37:47', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('177', '76', '45', '谢宇', NULL, '测试群3', '1', TO_DATE('2024-11-21 10:02:44', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 01:50:13', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('178', '76', '51', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试群3', '1', TO_DATE('2024-11-21 10:02:44', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 09:50:14', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_GROUP_MEMBER" VALUES ('179', '77', '45', '谢宇', NULL, '12312', '1', TO_DATE('2024-11-21 10:02:40', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2024-11-21 09:53:55', 'SYYYY-MM-DD HH24:MI:SS'));

-- ----------------------------
-- Table structure for IM_GROUP_MESSAGE
-- ----------------------------
DROP TABLE "MIP"."IM_GROUP_MESSAGE";
CREATE TABLE "MIP"."IM_GROUP_MESSAGE" (
  "ID" NUMBER(20,0) VISIBLE NOT NULL,
  "GROUP_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "SEND_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "SEND_NICK_NAME" NVARCHAR2(255) VISIBLE,
  "RECV_IDS" NCLOB VISIBLE,
  "CONTENT" NCLOB VISIBLE,
  "AT_USER_IDS" NCLOB VISIBLE,
  "RECEIPT" NUMBER(4,0) VISIBLE,
  "RECEIPT_OK" NUMBER(4,0) VISIBLE,
  "TYPE" NUMBER(4,0) VISIBLE NOT NULL,
  "STATUS" NUMBER(4,0) VISIBLE,
  "SEND_TIME" DATE VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."ID" IS 'id';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."GROUP_ID" IS '群id';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."SEND_ID" IS '发送用户id';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."SEND_NICK_NAME" IS '发送用户昵称';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."RECV_IDS" IS '接收用户id,逗号分隔，为空表示发给所有成员';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."CONTENT" IS '发送内容';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."AT_USER_IDS" IS '被@的用户id列表，逗号分隔';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."RECEIPT" IS '是否回执消息';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."RECEIPT_OK" IS '回执消息是否完成';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."TYPE" IS '消息类型 0:文字 1:图片 2:文件 3:语音 4:视频 10:系统提示';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."STATUS" IS '状态 0:未发出 1:已送达  2:撤回 3:已读 4:已接收';
COMMENT ON COLUMN "MIP"."IM_GROUP_MESSAGE"."SEND_TIME" IS '发送时间';
COMMENT ON TABLE "MIP"."IM_GROUP_MESSAGE" IS '群消息';

-- ----------------------------
-- Records of IM_GROUP_MESSAGE
-- ----------------------------

-- ----------------------------
-- Table structure for IM_PRIVATE_MESSAGE
-- ----------------------------
DROP TABLE "MIP"."IM_PRIVATE_MESSAGE";
CREATE TABLE "MIP"."IM_PRIVATE_MESSAGE" (
  "ID" NUMBER(20,0) VISIBLE NOT NULL,
  "SEND_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "RECV_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "CONTENT" NCLOB VISIBLE,
  "TYPE" NUMBER(4,0) VISIBLE NOT NULL,
  "STATUS" NUMBER(4,0) VISIBLE NOT NULL,
  "SEND_TIME" DATE VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_PRIVATE_MESSAGE"."ID" IS 'id';
COMMENT ON COLUMN "MIP"."IM_PRIVATE_MESSAGE"."SEND_ID" IS '发送用户id';
COMMENT ON COLUMN "MIP"."IM_PRIVATE_MESSAGE"."RECV_ID" IS '接收用户id';
COMMENT ON COLUMN "MIP"."IM_PRIVATE_MESSAGE"."CONTENT" IS '发送内容';
COMMENT ON COLUMN "MIP"."IM_PRIVATE_MESSAGE"."TYPE" IS '消息类型 0:文字 1:图片 2:文件 3:语音 10:系统提示';
COMMENT ON COLUMN "MIP"."IM_PRIVATE_MESSAGE"."STATUS" IS '状态 0:未发出 1:已送达  2:撤回 3:已读 4:已接收';
COMMENT ON COLUMN "MIP"."IM_PRIVATE_MESSAGE"."SEND_TIME" IS '发送时间';
COMMENT ON TABLE "MIP"."IM_PRIVATE_MESSAGE" IS '私聊消息';

-- ----------------------------
-- Records of IM_PRIVATE_MESSAGE
-- ----------------------------
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('856', '50', '41', 'f171304b996eb753231b5252167a7c62dcf8f5af637f7b930f6e124ff82b3afd690a96a43522a4e14aaf67794fbe0a2d465b707aad33ea40c3e401d7e3f4e64476eb3c0e4fd3b5fa1062ac4b251ac449bbdbf9a8aacd37bca2ba0ef628aacd432e9e64356bd2add710f8d88fe85f4c395bd953f398f1ae6d470754d7a1a904c26905e044870f2b8edf118837c1d39bed0f9e79b22e50a7b4b84b7569df41d5372312265d3f2182996968c1e3b116ef3c713f74f41643e251b00e32d2d9c0d680', '2', '2', TO_DATE('2025-01-24 14:40:05', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('857', '46', '50', '6e0c1f69b2796438b0b85a74e7c985a548b357eb19d319131db7bdfc2e0483449ccde75dc950c0fb068781046d36637d9e1f0352443023fec490352bef48831e50bd15956bbdf9f53286cb62469ca67d668c9f858ed13213e29855413f6b58c43a4c289936ecbd7870ad5d28c7d268169471ecfc5807f85d77102667b5178367f127abae893c376f50d760ff88f17995fdcba0b379e1239fda02e86e5ab7fc0a1440682c3b508f7e8f921f600184d184dbaf4b518b2d686ce3607a01cc622afff7508bcd956daf871750deea98b34eebaee97c667041e2c3ac2970ebaa0c849662e2b367e094df231f7329d8235c14e23d2676ece70a49f8f788635338ed5b25', '0', '3', TO_DATE('2025-02-06 14:47:41', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('858', '48', '38', '2d76449e2c080fc28cf2b6bf53d6bee2', '0', '3', TO_DATE('2025-02-10 15:47:51', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('859', '53', '44', '6ec04e9e3f278614b18fbbfd6567b87bce049705dcf8f56c44adf78f7351c4aa66794b2686c0efe8be3fb3e69f834dd38d40ddfa2a8573bab4f580f7bb1cda1233ed369256826808e06d7de2a05e6037354e80223c54b3b8a0fc1eba95ef7b98144ff2ceb534678c043835f5ba30026c436938a84d1c1f155da04a713d75ea90e6cc2b050c8eb5d0c4de8fe60b7d26b7e06c0ec61081c4a19f886b8af7f29c2ffee3bb6e1cf1cbcf6a2fe520579436e558aad8c5bece89c947e17b982cf81c4d8ccbe45300dc2b355a580613fa06ab4ab4f8f1b22cb7c25ab0f1d235bfbb4f3a6a3cdd7448e469dc4dd6835a0dfa578f003f99687061f9cc62d1372a0e7a2fa148c2fc1a2ebd2dfdbe0876bb9231193afdd4c7dd1adfc4bfcd9f1052f00882a6', '0', '3', TO_DATE('2025-02-19 14:29:58', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('860', '53', '44', 'fe6d74388719ce91bc04320162c31d5c5dc0b2c4318f88b67872284f6e09690cfeacd6a2fd50ae1d4c9a01aeb54eabf72e826c2aa0e2a945e7f0692c338d7e0306d3b5f6c2b2cd9fafdaf484799094e8f6b49211c782f1699fc864cae78d07761dce97977e2e9c9b8ca318b8607bcc758399098553c38a9e6031bb07126ca2705e9fc774def2b3174622127fd43f118d61ee41e1d9972a74b6a4e43b878675a3297aa0ccb973aff1a2088152f49d1220284e425eec3b51273b5089123f947ff64d3bb74814ac4951859542e79affca46fead86fb6f4b1c277f1fc0e162d8ff4c00eaf7c96042cb016b5b964ae59340fa27c107909c47cb1fbf1dda7dcd25be1d3672b9174adf729e482b998faec21cf0', '0', '3', TO_DATE('2025-02-19 14:30:16', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('861', '30', '44', '6e0c1f69b2796438b0b85a74e7c985a548b357eb19d319131db7bdfc2e0483449ccde75dc950c0fb068781046d36637d46270dc1c2aa67ec6f7783fa2918cdbbfcc1470da261cf4dceaba2777fa13b009c5ec5278a542137ff6fdad46bb79576', '0', '3', TO_DATE('2025-02-19 14:33:08', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('862', '30', '44', 'a38201dc434d59b00ece171af75614a8ceed92505ab9b1a7c0deab85c0b72f9265ed79cab7ad72d216c3effd911932f1df21f5fea57ab59cbd182c563a51cb6d', '0', '3', TO_DATE('2025-02-19 14:33:14', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('863', '30', '46', '6e0c1f69b2796438b0b85a74e7c985a548b357eb19d319131db7bdfc2e0483449ccde75dc950c0fb068781046d36637d46270dc1c2aa67ec6f7783fa2918cdbbfcc1470da261cf4dceaba2777fa13b009c5ec5278a542137ff6fdad46bb79576', '0', '3', TO_DATE('2025-02-20 16:24:15', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('864', '30', '46', 'a38201dc434d59b00ece171af75614a8ceed92505ab9b1a7c0deab85c0b72f9265ed79cab7ad72d216c3effd911932f1df21f5fea57ab59cbd182c563a51cb6d', '0', '3', TO_DATE('2025-02-20 16:24:19', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('865', '46', '44', '7123dc662412712fbc09a9af38b64c75', '0', '3', TO_DATE('2025-02-24 11:55:27', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('866', '46', '44', '04fba2c54314ebd34097c16a7ec34b2c10e9ae70069e629503fb6742cc9cfe5d', '0', '3', TO_DATE('2025-02-24 11:55:29', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('867', '46', '44', '569a06edc05a2fc86c55cce60790a68c9dec236f71af95eb3a6bf0ae4863d39f986e8b0c560f5eb0693a2510d62099db1f3bc62ebe5431266d306b347745dd647cae531faf7bb30deec8e4c6b400cbe6c0029af0cd7ae7d765fec8aabac42463', '0', '3', TO_DATE('2025-02-24 12:03:20', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('868', '44', '30', '382d5d2616e3d45d5fd1566c9289222c5a1721b4612df8c30782ecf4bdb06f2745e8c2acd3ddeca1762c1fb5b7f1a4105cad9f5e0aaa9351c29a0467a3aaeb5d', '0', '3', TO_DATE('2025-02-27 16:48:07', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('869', '57', '30', '3097c0cd34e55eef128a525791b7fbac', '0', '3', TO_DATE('2025-02-28 10:23:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('870', '58', '57', '931fdcc071f28a98f4cb7c94487ce85c', '0', '3', TO_DATE('2025-02-28 10:30:52', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('871', '53', '57', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-04 09:10:53', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('872', '38', '48', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-04 09:11:44', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('873', '38', '57', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-04 09:12:04', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('874', '58', '48', '931fdcc071f28a98f4cb7c94487ce85c', '0', '3', TO_DATE('2025-03-04 09:25:29', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('875', '57', '58', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-04 09:26:54', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('876', '48', '58', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-04 09:27:30', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('877', '48', '34', 'bdbf0e6c931d852b7d548ae163166214dad7cf0559ac75cd307218dedbc965bfdc287d3110402e69a04178f3fb89c0f2', '0', '3', TO_DATE('2025-03-04 10:47:21', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('878', '48', '34', 'b4afe400a0e874bddeba9e46182c91d7e2ef755c308158f8ece147b3e7c33377fba05deef0c6216b3b4c7962a0bfd5d7cc51579ab444ddb800c6239e40e271d6', '0', '3', TO_DATE('2025-03-04 10:56:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('879', '48', '36', 'ea86fe1e2b7289e42c3d9ba6501ad2e8cf80898baa72298bac2464c2cdcc96c06a6042bbe29d7017615d5f1c1706796540ae29fd22f2788a9e2a1be9fd7aa4c42ed6024fe0adb7fcc6bc7c8872608123577bd5ee6d8ac1ad0b4c274293359de8aefb0914e9c3410eb7f5fa36c7a69efa', '0', '3', TO_DATE('2025-03-04 16:48:42', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('880', '36', '48', 'd75c26e4e9c2608fc11c8b08415d1254', '0', '3', TO_DATE('2025-03-04 17:10:14', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('881', '36', '48', '7d1c92633bca6658d2a1c8b06b1dec24630100dabfb8c20e93ec8c2488d3a5f0', '0', '3', TO_DATE('2025-03-04 17:10:20', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('882', '36', '48', 'df5010275fea5e5dd58a593cefcabc67', '0', '3', TO_DATE('2025-03-04 17:10:29', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('883', '36', '48', '5586863e8af76c8e5be12acc6f9d8eec8fe62f35dd2185d266b8bf2195bc96a1', '0', '3', TO_DATE('2025-03-04 17:10:39', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('884', '36', '48', '8ef166a9e316b366c4c0d27b4b140876dec1163c09e6f23d931577f3a5f9b551', '0', '3', TO_DATE('2025-03-04 17:10:43', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('885', '48', '36', 'e6847461b5f97a154106e3c131c49bc030842cae8149c054067132a7c3fdf69d', '0', '1', TO_DATE('2025-03-05 09:49:51', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('886', '58', '30', '931fdcc071f28a98f4cb7c94487ce85c', '0', '3', TO_DATE('2025-03-05 15:10:24', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('887', '30', '58', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-05 15:10:40', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('888', '59', '30', '2805d862b37ebc5c88aa20a3a9ea1e76', '0', '3', TO_DATE('2025-03-05 15:13:30', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('889', '48', '30', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-05 15:17:12', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('890', '30', '44', 'ed677cad7e1c0a063a052ebdfded647787fdc8d6fbdc745746165fc3e5176c54', '0', '3', TO_DATE('2025-03-10 17:33:46', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('891', '30', '44', '2b096299d5cc878a22c998729e23d27f8454e4ad961e12731bb0a5a1b3731d7f9d877b2005185537372d9498df5d48b5c8dc635e9a3d4e1530dff0c314f073da', '0', '3', TO_DATE('2025-03-10 17:34:10', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('892', '59', '57', '931fdcc071f28a98f4cb7c94487ce85c', '0', '3', TO_DATE('2025-03-18 09:37:08', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('893', '57', '59', '28827ca8a74653adf7606c659d30951366c2ad965fb49664dde57e7601da1be9', '0', '2', TO_DATE('2025-03-18 09:37:19', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('894', '57', '59', '28827ca8a74653adf7606c659d309513071ddcb1c84ba2d1cd5b4ac0fadc0b2a', '0', '3', TO_DATE('2025-03-18 09:37:25', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('895', '53', '57', '4795ec9b1ac7e04f1c5fdbfc724bb82a', '0', '1', TO_DATE('2025-03-21 13:35:19', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('896', '53', '30', 'e2f9b4b2c5aa5f0b943b5a5acd888c5c', '0', '1', TO_DATE('2025-04-01 09:18:41', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('897', '53', '30', '881b800f7485f153cdf4badd4fd7956a5712d534034533cd8c15c994ed766ac598282db39b7baa9bb20a0ca7d772b9b201517d7d9d11dd1647df839ca47cb86783585db3005dfbf382ffc8c2e2c636e029d596c41734f6f35194c4719a8b1b115a94a11e2af68fa12dcc3b56b3961074ef4fb983d8ae20a41ded5d59d6d9e05d323fc54f5c1d6509bdec5e6093e70a1a01265794772b789558dc280cd23203a9a429cee264901d146f671fe9c73605d93672b9174adf729e482b998faec21cf0', '1', '1', TO_DATE('2025-04-01 09:19:07', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('898', '53', '30', '79ecf378b78db46ad2792b27fdf28a549fe8331538ad428dde7b44c7ab19e609', '0', '1', TO_DATE('2025-04-01 09:19:27', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('899', '53', '30', '399365e769440807fc7dd4022bfe5e34', '0', '1', TO_DATE('2025-04-01 09:19:56', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('900', '41', '48', 'd36b3b214f6890b6f4969b6daa2aa797849a6c94fb3f410ec28bb0751872fa2f27324979919f6af7bbf6c2b27858e3a412f18aaff695fe7577eca55e8dd7201e78ed7c7d765fb14bb18f56c0062362f731b976b56100772b2b523432d66672ee7fd658b33a266479da6ec594f5554438ddb5e265c098df5ad27b69c9ae6d2ada4b72e08bcde21119040725bd048a1fc7e8186ed0e1375bec7f9bc659e6c9bc2dc29ccfae5630e0f25c1148bc9975ff674600923455df6ad6ee14661022c3f1d2478e0702bb3ee03599903a2faf2a56ca', '2', '3', TO_DATE('2025-04-10 11:07:08', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('901', '41', '48', 'd36b3b214f6890b6f4969b6daa2aa797849a6c94fb3f410ec28bb0751872fa2f27324979919f6af7bbf6c2b27858e3a45b82b36ba31fff16a0ac412251fec2600e54cdca7e06166ec69a3ce3a3aab7593f8e6587828c0bbeb1b2945e17cde525398cf65a6c4011aa71fe00e045b034bfafd01856749e7e0b664d319f66c6a2d252d6eb040bbfb1e975c9ffd85de31b418f73bcc22e18e7b48c7a57e51a4d0f8c49844d8c5ccbde32d524a11e35624f5789aaac582738aa083b41dcfbbfe3e2e6a734414bab5b5be1298d2b28e66130e2', '2', '3', TO_DATE('2025-04-10 11:07:08', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('902', '48', '41', '0541cec3a16a368035c56292f4f9f5f7', '0', '3', TO_DATE('2025-04-10 11:09:26', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('903', '41', '32', '9d486901356c5df062a5412f6dc5ba39b3a225c95a1283cf4680b4097218aa9ebed91641f32bfb9571c96f8c9b48cc2e0d497b831581befbbd32a597e8755567d0429be9ee511da50134c1450aa2ed5db1f75362904413e33a52e861dca471d1e94075e122725ca306ec15abfbe9121d3d57c08b0af8ffd6a80e128efca7a6ff82075c37a92a8e8b67e9105d75636e81', '2', '1', TO_DATE('2025-04-10 14:56:45', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('904', '41', '32', '5a442432ad1f08e5f08556dfb5a4fd0c33ebcd288a48d19d2ff738e86a532a80079ec4ab3ddc2bbde99d4fbd79a6283ee4155ab55986c9fe105a2f50f175fad8d2e164c658bc9bac32a1ace9f8ed8c6b55616166682715b926ea897b0c963d9d9929c3a880088f54b19a73085df586fa9b527a1781c8b76c74a4c53536e067a61b9ff85b055e1f9bd47c2de6ce009e1c83df94aedd14d92ee2262c55d330c96c7a6438fe134680e7ae8656bb6c0f523876830428e7139b3e2d78d4b3b19a150e9a5f50afe26309db8c56cb864721ea00e604549537e574c377ede95719efee1c5fa689334ca2c217ca258c40f62720f3f72cdb3850893340865aa70f1a60643e2c58700b60e4a7677693734935c35263aad36f72a57dde9a807ca5318dbee49c3c32b93b1d1bacb1ee27decccc2df171fdc4edcc405ad9f81015622b3ec8c3e7', '0', '1', TO_DATE('2025-04-10 15:02:05', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('905', '41', '32', '881b800f7485f153cdf4badd4fd7956a5712d534034533cd8c15c994ed766ac598282db39b7baa9bb20a0ca7d772b9b201517d7d9d11dd1647df839ca47cb86755372b7c3d6e9caeb9600b5ca494ebbfad3346942847f760dd9b8c018895f3b25a94a11e2af68fa12dcc3b56b3961074ef4fb983d8ae20a41ded5d59d6d9e05d323fc54f5c1d6509bdec5e6093e70a1a66349967346cdf5c8071619d9362c2546bf7546f16d0f8a3bb9f751555ae9bb43672b9174adf729e482b998faec21cf0', '1', '1', TO_DATE('2025-04-10 15:29:54', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('906', '41', '32', 'e4ab051c8f9e39c4e123193b8229d000cce2496b5db0161fd0651a454b1f9c5889d8df5415fe64aa6a483d276d59aa1f4f0bbf23d894a9e824991d3f0178b23d9b6da0bae04a77f0750b563b06bab50671c47bbb4bf055e53659f924c68530d2d1e16ee54bf5dfb49da2914402fd4a0a98d71b2ae1236a513d86c14930f361a0b524bbb2e5d74ced0dfe938877b721d7de49b03f2a302ace3c64a0d8a55ff766ef47a9195dcc3535224b347ab85bbcae45fbaaa3f60c0729bc736ca8dd8890a0421289cb1a09d5f91f747a1bdf14d58db8fce58acf2966ce4a2c2810757ba9c770b145015dc75826df319601d98a5456af8d435eff6f1bb7cbe4358be30d4eb382129d2d680ba409784f836bd3d985729e70166f4349694895b44b17cfdca60dd0f253c44abd47ec87abffc9d178317ff56973d6ec74acda13188065e57e0afead57ffefcd09c8daee5a781f163126ee50bd81eb565fb328050b80e850ec268ab98ac1f963b0b0ab7e63be66f212e3468af8f66442d67ae303678abf9c49580ce1c8a97ca7a0145903f8a5327e62d97dea823950e70ce35dee2824a37cde4e0ddde41be167e31a2ff20de8c22f4df7487682ae779aa5400002d4f5adbbf38210db0f373c681624309633568368e61a1db4dae0095ce655f18b4479b08f80a975d4172fec27b93411fec4993aaf6e117f4d59064c94099c75df404d1c522fa238b9b45b40a055b347a2f5c22b494bc1c1', '0', '1', TO_DATE('2025-04-10 15:30:48', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "MIP"."IM_PRIVATE_MESSAGE" VALUES ('907', '41', '32', '2455ba8ce0e8a7aa6746942d139f4d60b82956658d12ea0007ecc8e79bcfb0b15a595679deb9a178d0bcfe9bc141162150ddb9b438715bad55b754c37fc2b18019edfee7e246c7f67e592b2123e23d4d6eb933ab830a42cdafff5803ba6831575ce581b63e4a695eaafd14601e2cf2405d72881d7bbe12985f06b3177197eb4ba976f583b15ee49971c84147d1b27850', '2', '1', TO_DATE('2025-04-10 15:37:18', 'SYYYY-MM-DD HH24:MI:SS'));

-- ----------------------------
-- Table structure for IM_USER
-- ----------------------------
DROP TABLE "MIP"."IM_USER";
CREATE TABLE "MIP"."IM_USER" (
  "ID" NUMBER(20,0) VISIBLE NOT NULL,
  "USER_NAME" NVARCHAR2(255) VISIBLE NOT NULL,
  "NICK_NAME" NVARCHAR2(255) VISIBLE NOT NULL,
  "HEAD_IMAGE" NVARCHAR2(255) VISIBLE,
  "HEAD_IMAGE_THUMB" NVARCHAR2(255) VISIBLE,
  "PASSWORD" NVARCHAR2(255) VISIBLE NOT NULL,
  "SEX" NUMBER(4,0) VISIBLE,
  "TYPE" NUMBER(6,0) VISIBLE,
  "SIGNATURE" NCLOB VISIBLE,
  "LAST_LOGIN_TIME" DATE VISIBLE,
  "CREATED_TIME" DATE VISIBLE,
  "MOBILE" NVARCHAR2(11) VISIBLE,
  "POST_TITLE" NVARCHAR2(100) VISIBLE,
  "EMAIL" NVARCHAR2(100) VISIBLE,
  "EXPIRE_DAYS" NUMBER(11,0) VISIBLE,
  "ROLE" NVARCHAR2(100) VISIBLE NOT NULL,
  "CHAT_OBJECT_IDS" NCLOB VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_USER"."ID" IS 'id';
COMMENT ON COLUMN "MIP"."IM_USER"."USER_NAME" IS '用户名';
COMMENT ON COLUMN "MIP"."IM_USER"."NICK_NAME" IS '用户昵称';
COMMENT ON COLUMN "MIP"."IM_USER"."HEAD_IMAGE" IS '用户头像';
COMMENT ON COLUMN "MIP"."IM_USER"."HEAD_IMAGE_THUMB" IS '用户头像缩略图';
COMMENT ON COLUMN "MIP"."IM_USER"."PASSWORD" IS '密码(明文)';
COMMENT ON COLUMN "MIP"."IM_USER"."SEX" IS '性别 0:男 1:女';
COMMENT ON COLUMN "MIP"."IM_USER"."TYPE" IS '用户类型 1:普通用户 2:审核账户';
COMMENT ON COLUMN "MIP"."IM_USER"."SIGNATURE" IS '个性签名';
COMMENT ON COLUMN "MIP"."IM_USER"."LAST_LOGIN_TIME" IS '最后登录时间';
COMMENT ON COLUMN "MIP"."IM_USER"."CREATED_TIME" IS '创建时间';
COMMENT ON COLUMN "MIP"."IM_USER"."MOBILE" IS '手机号';
COMMENT ON COLUMN "MIP"."IM_USER"."POST_TITLE" IS '岗位职称';
COMMENT ON COLUMN "MIP"."IM_USER"."EMAIL" IS '邮箱';
COMMENT ON COLUMN "MIP"."IM_USER"."EXPIRE_DAYS" IS '过期天数';
COMMENT ON COLUMN "MIP"."IM_USER"."ROLE" IS '用户角色，固定三种角色：管理员、普通用户、临时用户';
COMMENT ON COLUMN "MIP"."IM_USER"."CHAT_OBJECT_IDS" IS '临时用户聊天对象Id，多个对象用逗号隔开';
COMMENT ON TABLE "MIP"."IM_USER" IS '用户';

-- ----------------------------
-- Records of IM_USER
-- ----------------------------
INSERT INTO "MIP"."IM_USER" VALUES ('1', 'huangjie', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '$2a$10$BXEZqIS2l6PuSYeD6QhDnOWTtvh7OyNbKUt5QQRCmRJJ9rUApeSry', '0', '1', '哈哈哈哈哈', NULL, TO_DATE('2024-05-13 01:51:01', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('30', 'chen_jun', '陈俊', NULL, NULL, '$2a$10$kIVe.De2hJiAkWw3ilIqOOBmJpFCiwolnAMeo4wzqI/373kLSZ/Cq', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:44:46', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('31', 'deng_xiaoling', '邓小玲', NULL, NULL, '$2a$10$33/GGnsyJ9bPjbs4J/sweuYlwC5g3MSFpF3mrIU2cALXC2kuYb6ji', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, 'BOSS', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('32', 'dou_yi', '豆易', NULL, NULL, '$2a$10$gWWnOpRDDnhipwMUf5nJzugBVqnmZxNouYypbLBpdC0lqjr2CiECW', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:48:35', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '项目经理', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('33', 'he_ping', '何平', 'https://***************:9100/file/box-im/image/20241127/1732695558532.jpg', 'https://***************:9100/file/box-im/image/20241127/1732695558686.jpg', '$2a$10$BRLVgv67PZP20/Lh0GHSAeiKEiWBFdZ6Fq/9Rry5MCRZQ4tzi/LFe', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:50:09', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('34', 'hu_jingling', '胡晶玲', NULL, NULL, '$2a$10$ktJrY0wU.TeoM6ahF47kJ.tCA7AVK08ksoORDjmdsqsdOiE52kJKO', '1', '1', NULL, NULL, TO_DATE('2024-11-01 08:50:36', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('35', 'jiang_tengde', '姜腾德', NULL, NULL, '$2a$10$wA/jnasmGOUVc99e/ztTTes/yx.2KViWZJvdDJq2Z/a/K/H0Lk6Pu', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:51:11', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('36', 'li_jiankui', '李建奎', NULL, NULL, '$2a$10$l44op8mp8hnVzQKroI28RuMxfT9ibgMsaVAIjHrmkQtE69HZOG99O', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:51:40', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('37', 'li_ruiling', '李蕊伶', 'https://***************:9100/file/box-im/image/20241108/1731031402211.png', 'https://***************:9100/file/box-im/image/20241108/1731031402633.png', '$2a$10$zDpRBaEIZxlhVm50.Z.d..gASaZGqPIcLPn2bGNbIMN3hc9GMmES6', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:52:01', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('38', 'li_xin', '李欣', 'https://***************:9100/file/box-im/image/20241108/1731031322654.png', 'https://***************:9100/file/box-im/image/20241108/1731031323138.png', '$2a$10$vnf4.BmZwQbolyO4egI7beIw4ccGFKDeIXeU.tGf0aOwZV.KU1vS2', '1', '1', '牛，马累了会休息；牛马累了只会给自己点两杯咖啡', NULL, TO_DATE('2024-11-01 08:52:34', 'SYYYY-MM-DD HH24:MI:SS'), '18408260038', 'BOSS', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('39', 'sun_yang', '孙杨', NULL, NULL, '$2a$10$LEbmQ8QRGWBxYcl7LXiZ1ueS/DkmgcGgWlMhZhOuCUcNawpa6ZIva', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:53:01', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('40', 'tan_jun', '谭军', NULL, NULL, '$2a$10$BcSVewiUgwgzUIVgeJa.JeX/WPe7k9c12JJdHGCa2A2auYvRHh6Q2', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:53:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('41', 'wang_xing', '王星', NULL, NULL, '$2a$10$IU0/Hbjci4CJumLH3V9X0u16sDolUCt/eimCZttoBSjDaSJcRBL.a', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:53:53', 'SYYYY-MM-DD HH24:MI:SS'), NULL, 'BOSS', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('42', 'wei_xinyuan', '卫新元', 'https://***************:9100/file/box-im/image/20241108/1731031324596.jpg', 'https://***************:9100/file/box-im/image/20241108/1731031324737.jpg', '$2a$10$GcipCQk82trB/Fj8EpLP4uzCsGcgguJLuoK1UH2aKxG2IbACMr7Py', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:54:13', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('43', 'wu_fengyang', '伍枫飏', NULL, NULL, '$2a$10$xaOnSFuNp/di0ccKuSAo3emsc.9bekh5plviG3K5U4r0xYFEw/oba', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:54:33', 'SYYYY-MM-DD HH24:MI:SS'), NULL, 'UI', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('44', 'xiao_fu', '骁釜', NULL, NULL, '$2a$10$IQLIE07i8Yo2vciwZNLQDeEko.p6lhLIhV1h07HeM1xGu1qVNEpMW', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:55:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '大数据', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('45', 'xie_yu', '谢宇', NULL, NULL, '$2a$10$RHbMhxa0Amwn02hc6U5SUOOR/O8NjS7EaJkbBrmGkN8m4zuH3a6Q.', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:55:54', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('46', 'xu_miao', '徐苗', NULL, NULL, '$2a$10$AujQ0J5N3Elr/T7OpSpAb.uyTWvemIClJm.96b1b29gDRdjynfn5a', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:56:14', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '	<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('47', 'xu_shan', '胥珊', 'https://***************:9100/file/box-im/image/20241203/1733212714853.png', 'https://***************:9100/file/box-im/image/20241203/1733212715427.png', '$2a$10$5/.7j.VZT1/.4rxrUjLGwu8UNbPCNMzoPeToswghqX7ilxZxcidQ2', '0', '1', 'ddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd', NULL, TO_DATE('2024-11-01 08:56:43', 'SYYYY-MM-DD HH24:MI:SS'), '15245454545', '运维', '	<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('48', 'yang_jiong', '杨迥', NULL, NULL, '$2a$10$3as.39boEdzMOvutNdgKXu67H30kAijeNtJOj.f3L4CULCbkKUEwW', '0', '1', '黑暗笼罩万物，我将是黑暗中最后的那道曙光，以雷霆，击碎黑暗！', NULL, TO_DATE('2024-11-01 08:57:10', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('50', 'zhang_daojian', '张道见', NULL, NULL, '$2a$10$S82MyOjN3lxRFGCwpFqLGeGYtirFyp6ehwK76XgMhIOfuPcdAvq.u', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:57:55', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '项目经理', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('51', 'zhao_xing', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '$2a$10$vsp91MUYpPklYRhCn5ubl.4Dd8BdosQupXjMcnHW3KzzM1/b4Ses2', '0', '1', 'haahah', NULL, TO_DATE('2024-11-01 08:58:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('52', 'zou_yunchuan', '邹云川', NULL, NULL, '$2a$10$3E/lo89nv9fIwBqzPIF2feweh0za3M8A4C6TDJJhYkRjK3kfgltLe', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:58:44', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '产品经理', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('53', 'admin', '管理员', NULL, NULL, '$2a$10$jZpJjA2QuCa0VTOGicbIO.96eMNC./VwZGVTMyGY8mIgF21RPKo3u', '0', '1', NULL, NULL, TO_DATE('2024-11-01 09:04:35', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '管理员', NULL, NULL, '管理员', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('54', 'shen_xuchao', '沈序超', NULL, NULL, '$2a$10$I2CG4ZeXcj7dtBZEQSgLU.7px3i0DmBhmEI7KIpJUSoJrmQ4U.jKC', '0', '1', NULL, NULL, TO_DATE('2024-11-01 09:14:03', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '售前', NULL, NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('55', 'ceshi', '临时用户', NULL, NULL, '$2a$10$fRB9mywstHgrPy.w1UBcDO5csr6n422LAqqHx7HtpdG0JN2mcWl4K', '0', '1', NULL, NULL, TO_DATE('2024-11-21 03:51:09', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, '30', '临时用户', '[{"deptCode":"YLSYB","deptName":"医疗事业部","groupName":"佳缘科技","nickName":"谢宇","organizationName":"成都","userId":45,"userName":"xie_yu"},{"deptCode":"YLSYB","deptName":"医疗事业部","groupName":"佳缘科技","nickName":"管理员","organizationName":"成都","userId":53,"userName":"admin"},{"deptCode":"YLSYB","deptName":"医疗事业部","groupName":"佳缘科技","nickName":"赵星","organizationName":"成都","userId":51,"userName":"zhao_xing"}]');
INSERT INTO "MIP"."IM_USER" VALUES ('56', 'li_rui', '李锐', NULL, NULL, '$2a$10$ktJrY0wU.TeoM6ahF47kJ.tCA7AVK08ksoORDjmdsqsdOiE52kJKO', '0', '1', NULL, NULL, TO_DATE('2024-12-03 09:32:09', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('57', 'yu_chenghao', '宇成浩', NULL, NULL, '$2a$10$I3ndU.UdNYlH8wUyqI/wZewRmiw5dl9bhbDPSYD6PYqmpGeBrgTaO', '0', '1', NULL, NULL, TO_DATE('2025-02-28 02:21:58', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('58', 'zou_luncong', '邹伦聪', NULL, NULL, '$2a$10$JOykfAuPvYjOIiffvMQzWeoDU6XC1LlEPS9kLMiEPHVtmediHRz2a', '0', '1', NULL, NULL, TO_DATE('2025-02-28 02:22:45', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);
INSERT INTO "MIP"."IM_USER" VALUES ('59', 'li_yuliang', '李余亮', NULL, NULL, '$2a$10$IZzmn.x8l3AOqJL8OwJhke/AkHZlhjYucgtKYPnymAtgNaryHZwkG', '0', '1', NULL, NULL, TO_DATE('2025-03-04 01:12:37', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);

-- ----------------------------
-- Table structure for IM_USER_DEPT
-- ----------------------------
DROP TABLE "MIP"."IM_USER_DEPT";
CREATE TABLE "MIP"."IM_USER_DEPT" (
  "GROUP_NAME" NVARCHAR2(100) VISIBLE,
  "ORGANIZATION_NAME" NVARCHAR2(100) VISIBLE,
  "USER_DEPT_ID" NUMBER(11,0) VISIBLE NOT NULL,
  "USER_NAME" NVARCHAR2(255) VISIBLE,
  "DEPT_CODE" NVARCHAR2(50) VISIBLE,
  "SYS_ID" NVARCHAR2(50) VISIBLE
)
TABLESPACE "MIPDW_DATA"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "MIP"."IM_USER_DEPT"."GROUP_NAME" IS '集团名称';
COMMENT ON COLUMN "MIP"."IM_USER_DEPT"."ORGANIZATION_NAME" IS '组织名称';
COMMENT ON COLUMN "MIP"."IM_USER_DEPT"."USER_DEPT_ID" IS '用户科室id';
COMMENT ON COLUMN "MIP"."IM_USER_DEPT"."USER_NAME" IS '用户名称';
COMMENT ON COLUMN "MIP"."IM_USER_DEPT"."DEPT_CODE" IS '科室编码';
COMMENT ON COLUMN "MIP"."IM_USER_DEPT"."SYS_ID" IS '系统编码';
COMMENT ON TABLE "MIP"."IM_USER_DEPT" IS '用户部门关系表';

-- ----------------------------
-- Records of IM_USER_DEPT
-- ----------------------------
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '81', 'jiang_tengde', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '82', 'li_jiankui', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '85', 'sun_yang', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '86', 'tan_jun', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '87', 'wang_xing', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '89', 'wu_fengyang', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '90', 'xiao_fu', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '91', 'xie_yu', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '92', 'xu_miao', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '96', 'zhang_daojian', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '98', 'zou_yunchuan', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '99', 'deng_xiaoling', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '100', 'chen_jun', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '103', 'dou_yi', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '105', 'shen_xuchao', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '108', 'huang_jie', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '111', 'hu_jingling', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '113', 'yang_jiong', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '115', 'li_xin', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '116', 'wei_xinyuan', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '117', 'li_ruiling', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '120', 'admin', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '121', 'zhao_xing', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '122', 'ceshi', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '123', 'he_ping', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '124', 'huangjie', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '127', 'xu_shan', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '128', 'li_rui', 'YLSYB', 'IM');
INSERT INTO "MIP"."IM_USER_DEPT" VALUES ('佳缘科技', '成都', '129', 'yu_chenghao', 'YLSYB', 'IM');

-- ----------------------------
-- Checks structure for table IM_DEPT
-- ----------------------------
ALTER TABLE "MIP"."IM_DEPT" ADD CONSTRAINT "SYS_C0041414" CHECK ("DEPT_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table IM_DEPT
-- ----------------------------
CREATE INDEX "MIP"."IM_DEPT_DEPT_CODE_IDX"
  ON "MIP"."IM_DEPT" ("DELETED_FLAG" ASC, "DEPT_CODE" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table IM_FRIEND
-- ----------------------------
ALTER TABLE "MIP"."IM_FRIEND" ADD CONSTRAINT "SYS_C0041440" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table IM_FRIEND
-- ----------------------------
ALTER TABLE "MIP"."IM_FRIEND" ADD CONSTRAINT "SYS_C0041415" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_FRIEND" ADD CONSTRAINT "SYS_C0041416" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_FRIEND" ADD CONSTRAINT "SYS_C0041417" CHECK ("FRIEND_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_FRIEND" ADD CONSTRAINT "SYS_C0041418" CHECK ("FRIEND_NICK_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table IM_FRIEND
-- ----------------------------
CREATE INDEX "MIP"."IDX_FRIEND_ID"
  ON "MIP"."IM_FRIEND" ("FRIEND_ID" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE INDEX "MIP"."IDX_USER_ID"
  ON "MIP"."IM_FRIEND" ("USER_ID" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table IM_GROUP
-- ----------------------------
ALTER TABLE "MIP"."IM_GROUP" ADD CONSTRAINT "SYS_C0041445" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table IM_GROUP
-- ----------------------------
ALTER TABLE "MIP"."IM_GROUP" ADD CONSTRAINT "SYS_C0041419" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_GROUP" ADD CONSTRAINT "SYS_C0041420" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_GROUP" ADD CONSTRAINT "SYS_C0041421" CHECK ("OWNER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table IM_GROUP_MEMBER
-- ----------------------------
ALTER TABLE "MIP"."IM_GROUP_MEMBER" ADD CONSTRAINT "SYS_C0041444" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table IM_GROUP_MEMBER
-- ----------------------------
ALTER TABLE "MIP"."IM_GROUP_MEMBER" ADD CONSTRAINT "SYS_C0041422" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_GROUP_MEMBER" ADD CONSTRAINT "SYS_C0041423" CHECK ("GROUP_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_GROUP_MEMBER" ADD CONSTRAINT "SYS_C0041424" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table IM_GROUP_MEMBER
-- ----------------------------
CREATE UNIQUE INDEX "MIP"."IM_GROUP_MEMBER_UN"
  ON "MIP"."IM_GROUP_MEMBER" ("GROUP_ID" ASC, "QUIT" ASC, "USER_ID" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table IM_GROUP_MESSAGE
-- ----------------------------
ALTER TABLE "MIP"."IM_GROUP_MESSAGE" ADD CONSTRAINT "SYS_C0041441" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table IM_GROUP_MESSAGE
-- ----------------------------
ALTER TABLE "MIP"."IM_GROUP_MESSAGE" ADD CONSTRAINT "SYS_C0041425" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_GROUP_MESSAGE" ADD CONSTRAINT "SYS_C0041426" CHECK ("GROUP_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_GROUP_MESSAGE" ADD CONSTRAINT "SYS_C0041427" CHECK ("SEND_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_GROUP_MESSAGE" ADD CONSTRAINT "SYS_C0041428" CHECK ("TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table IM_GROUP_MESSAGE
-- ----------------------------
CREATE INDEX "MIP"."IDX_GROUP_ID"
  ON "MIP"."IM_GROUP_MESSAGE" ("GROUP_ID" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table IM_PRIVATE_MESSAGE
-- ----------------------------
ALTER TABLE "MIP"."IM_PRIVATE_MESSAGE" ADD CONSTRAINT "SYS_C0041442" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table IM_PRIVATE_MESSAGE
-- ----------------------------
ALTER TABLE "MIP"."IM_PRIVATE_MESSAGE" ADD CONSTRAINT "SYS_C0041429" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_PRIVATE_MESSAGE" ADD CONSTRAINT "SYS_C0041430" CHECK ("SEND_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_PRIVATE_MESSAGE" ADD CONSTRAINT "SYS_C0041431" CHECK ("RECV_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_PRIVATE_MESSAGE" ADD CONSTRAINT "SYS_C0041432" CHECK ("TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_PRIVATE_MESSAGE" ADD CONSTRAINT "SYS_C0041433" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table IM_PRIVATE_MESSAGE
-- ----------------------------
CREATE INDEX "MIP"."IDX_SEND_RECV_ID"
  ON "MIP"."IM_PRIVATE_MESSAGE" ("RECV_ID" ASC, "SEND_ID" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table IM_USER
-- ----------------------------
ALTER TABLE "MIP"."IM_USER" ADD CONSTRAINT "SYS_C0041443" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table IM_USER
-- ----------------------------
ALTER TABLE "MIP"."IM_USER" ADD CONSTRAINT "SYS_C0041434" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_USER" ADD CONSTRAINT "SYS_C0041435" CHECK ("USER_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_USER" ADD CONSTRAINT "SYS_C0041436" CHECK ("NICK_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_USER" ADD CONSTRAINT "SYS_C0041437" CHECK ("PASSWORD" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "MIP"."IM_USER" ADD CONSTRAINT "SYS_C0041438" CHECK ("ROLE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table IM_USER
-- ----------------------------
CREATE INDEX "MIP"."IDX_NICK_NAME"
  ON "MIP"."IM_USER" ("NICK_NAME" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE UNIQUE INDEX "MIP"."IDX_USER_NAME"
  ON "MIP"."IM_USER" ("USER_NAME" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table IM_USER_DEPT
-- ----------------------------
ALTER TABLE "MIP"."IM_USER_DEPT" ADD CONSTRAINT "SYS_C0041446" PRIMARY KEY ("USER_DEPT_ID");

-- ----------------------------
-- Checks structure for table IM_USER_DEPT
-- ----------------------------
ALTER TABLE "MIP"."IM_USER_DEPT" ADD CONSTRAINT "SYS_C0041439" CHECK ("USER_DEPT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table IM_USER_DEPT
-- ----------------------------
CREATE INDEX "MIP"."IM_USER_DEPT_DEPT_CODE_IDX"
  ON "MIP"."IM_USER_DEPT" ("DEPT_CODE" ASC, "SYS_ID" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE INDEX "MIP"."IM_USER_DEPT_USER_NAME_IDX"
  ON "MIP"."IM_USER_DEPT" ("USER_NAME" ASC, "SYS_ID" ASC)
  LOGGING
  TABLESPACE "MIPDW_DATA"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
