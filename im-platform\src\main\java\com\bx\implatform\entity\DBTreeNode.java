package com.bx.implatform.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 树形实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/18 10:29:34
 */
@Data
public class DBTreeNode {
    private Integer id;
    private String name;
    private Integer pid;
    private Map<String,Object> extra;
    //子节点数组 必须先初始化，否则往里面加数据的时候会报空指针异常
    private List<DBTreeNode> children = new ArrayList<>();

}
