package com.bx.implatform.controller;

import com.bx.implatform.dto.UserDeptDTO;
import com.bx.implatform.result.Result;
import com.bx.implatform.service.UserDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "组织结构管理")
@RestController
@RequestMapping("/organizational")
@RequiredArgsConstructor
public class OrganizationalStructureController {

    private final UserDeptService userDeptService;

    @PostMapping("/list")
    @ApiOperation(value = "组织结构列表", notes = "获取组织结构列表")
    public Result list(@Valid @RequestBody UserDeptDTO userDeptDTO) {
        return userDeptService.getOrganizationalStructure(userDeptDTO);
    }

    @PostMapping("/getOnlyOrganizationalStructure")
    @ApiOperation(value = "获取组织结构（不含成员）", notes = "")
    public Result getOnlyOrganizationalStructure(@Valid @RequestBody UserDeptDTO userDeptDTO) {
        return userDeptService.getOnlyOrganizationalStructure(userDeptDTO);
    }
}

