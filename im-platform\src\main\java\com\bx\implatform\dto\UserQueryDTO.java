package com.bx.implatform.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("临时用户DTO")
public class UserQueryDTO {
    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /**
     * 用户角色，固定三种角色：管理员、普通用户、临时用户
     */
    @ApiModelProperty(value = "用户角色，固定三种角色：管理员、普通用户、临时用户")
    private String role = "普通用户";

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer pageNum = 1;

    /**
     * 每页显示数量
     */
    @ApiModelProperty(value = "每页显示数量")
    private Integer pageSize = 10;

}
