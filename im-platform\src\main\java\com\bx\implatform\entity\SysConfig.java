package com.bx.implatform.entity;

import com.bx.implatform.contant.Constant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2021/9/16 10:50
 */
@ApiModel(value = "SysConfig系统配置表")
@Data
public class SysConfig implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Integer id;

    /**
     * 模块ID
     */
    @NotBlank
    @ApiModelProperty(value = "模块ID")
    private String moduleId;

    /**
     * 模块名称
     */
    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    /**
     * 配置编码
     */
    @NotBlank
    @ApiModelProperty(value = "配置编码")
    private String configCode;

    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称")
    private String configName;

    /**
     * 配置说明
     */
    @ApiModelProperty(value = "配置说明")
    private String description;

    /**
     * 属性值
     */
    @NotBlank
    @ApiModelProperty(value = "属性值")
    private String configValue;

    /**
     * 配置类型 1：文本 2：布尔值 3：数字 4：日期
     */
    @ApiModelProperty(value = "配置类型 1：文本 2：布尔值 3：数字 4：日期")
    private String configType;

    /**
     * 排序
     */
    @NotBlank
    @ApiModelProperty(value = "排序")
    private Integer sortCode;

    /**
     * 删除标志 0：否 1：是
     */
    @ApiModelProperty(value = "删除标志 0：否 1：是")
    private String deletedFlag;

    /**
     * 创建人
     */
    @ApiModelProperty(hidden = true)
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")    //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ApiModelProperty(hidden = true)
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")    //后端-->前端。
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 系统ID
     */
    @ApiModelProperty(hidden = true)
    private String sysId = Constant.SYS_NAME;

    /**
     * 界面是否展示 Y：是 N：否
     */
    @ApiModelProperty(hidden = true)
    private String webShow = "Y";

    private static final long serialVersionUID = 1L;
}