package com.bx.implatform.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.imclient.IMClient;
import com.bx.imcommon.enums.IMTerminalType;
import com.bx.imcommon.util.JwtUtil;
import com.bx.implatform.config.JwtProperties;
import com.bx.implatform.contant.RedisKey;
import com.bx.implatform.dto.LoginDTO;
import com.bx.implatform.dto.ModifyPwdDTO;
import com.bx.implatform.dto.RegisterDTO;
import com.bx.implatform.dto.ResetPasswordDTO;
import com.bx.implatform.dto.UserDeptDTO;
import com.bx.implatform.dto.UserQueryDTO;
import com.bx.implatform.entity.Friend;
import com.bx.implatform.entity.GroupMember;
import com.bx.implatform.entity.GroupMessage;
import com.bx.implatform.entity.PrivateMessage;
import com.bx.implatform.entity.User;
import com.bx.implatform.entity.UserDept;
import com.bx.implatform.enums.ResultCode;
import com.bx.implatform.exception.GlobalException;
import com.bx.implatform.mapper.UserDeptMapper;
import com.bx.implatform.mapper.UserMapper;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.IFriendService;
import com.bx.implatform.service.IGroupMemberService;
import com.bx.implatform.service.IGroupMessageService;
import com.bx.implatform.service.IPrivateMessageService;
import com.bx.implatform.service.IUserService;
import com.bx.implatform.service.UserDeptService;
import com.bx.implatform.session.SessionContext;
import com.bx.implatform.session.UserSession;
import com.bx.implatform.util.*;
import com.bx.implatform.vo.LoginVO;
import com.bx.implatform.vo.OnlineTerminalVO;
import com.bx.implatform.vo.UserDeptVO;
import com.bx.implatform.vo.UserVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.jsonwebtoken.Claims;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    private final PasswordEncoder passwordEncoder;
    private final IGroupMemberService groupMemberService;
    private final IFriendService friendService;
    private final JwtProperties jwtProperties;
    private final IMClient imClient;
    private final RedisTemplate<String, Object> redisTemplate;
    @Autowired
    @Lazy
    private IPrivateMessageService privateMessageService;
    @Autowired
    @Lazy
    private IGroupMessageService groupMessageService;
    @Autowired
    private UserDeptMapper userDeptMapper;
    @Autowired
    private UserDeptService userDeptService;


    @Override
    public LoginVO login(LoginDTO dto) {
        User user = this.findUserByUserName(dto.getUserName());
        if (null == user) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "用户不存在");
        }
        if (!passwordEncoder.matches(dto.getPassword(), user.getPassword())) {
            throw new GlobalException(ResultCode.PASSWOR_ERROR);
        }
        // 生成token
        UserSession session = BeanUtils.copyProperties(user, UserSession.class);
        session.setUserId(user.getId());
        session.setTerminal(dto.getTerminal());
        String strJson = JSON.toJSONString(session);
        String accessToken = JwtUtil.sign(user.getId(), strJson, jwtProperties.getAccessTokenExpireIn(), jwtProperties.getAccessTokenSecret());
        String refreshToken = JwtUtil.sign(user.getId(), strJson, jwtProperties.getRefreshTokenExpireIn(), jwtProperties.getRefreshTokenSecret());
        LoginVO vo = new LoginVO();
        vo.setAccessToken(accessToken);
        vo.setAccessTokenExpiresIn(jwtProperties.getAccessTokenExpireIn());
        vo.setRefreshToken(refreshToken);
        vo.setRefreshTokenExpiresIn(jwtProperties.getRefreshTokenExpireIn());
        return vo;
    }

    @Override
    public LoginVO refreshToken(String refreshToken) {
        //验证 token
        if (!JwtUtil.checkSign(refreshToken, jwtProperties.getRefreshTokenSecret())) {
            throw new GlobalException("refreshToken无效或已过期");
        }
        String strJson = JwtUtil.getInfo(refreshToken);
        Long userId = JwtUtil.getUserId(refreshToken);
        String accessToken = JwtUtil.sign(userId, strJson, jwtProperties.getAccessTokenExpireIn(), jwtProperties.getAccessTokenSecret());
        String newRefreshToken = JwtUtil.sign(userId, strJson, jwtProperties.getRefreshTokenExpireIn(), jwtProperties.getRefreshTokenSecret());
        LoginVO vo = new LoginVO();
        vo.setAccessToken(accessToken);
        vo.setAccessTokenExpiresIn(jwtProperties.getAccessTokenExpireIn());
        vo.setRefreshToken(newRefreshToken);
        vo.setRefreshTokenExpiresIn(jwtProperties.getRefreshTokenExpireIn());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void register(RegisterDTO dto) {
        User user = this.findUserByUserName(dto.getUserName());
        if (null != user) {
            throw new GlobalException(ResultCode.USERNAME_ALREADY_REGISTER);
        }
        user = BeanUtils.copyProperties(dto, User.class);
        if ("临时用户".equals(user.getRole())) {
            user.setChatObjectIds(JSONObject.toJSONString(dto.getConnectionPersons()));
        }
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        this.save(user);
        log.info("注册用户，用户id:{},用户名:{},昵称:{}", user.getId(), dto.getUserName(), dto.getNickName());

        //如果是普通用户和管理员，并且有部门，则需要添加部门
        List<UserDeptVO> userDepts = dto.getUserDepts();
        if ("临时用户".equals(user.getRole()) || CollUtil.isEmpty(userDepts)) {
            return;
        }
        for (UserDeptVO userDept : userDepts) {
            userDept.setUserName(dto.getUserName());
        }
        List<UserDept> depts = MapperUtils.INSTANCE.mapAsList(UserDept.class, userDepts);
        userDeptService.saveBatch(depts);
    }

    @Override
    public void modifyPassword(ModifyPwdDTO dto) {
        UserSession session = SessionContext.getSession();
        User user = this.getById(session.getUserId());
        if (!passwordEncoder.matches(dto.getOldPassword(), user.getPassword())) {
            throw new GlobalException("旧密码不正确");
        }
        user.setPassword(passwordEncoder.encode(dto.getNewPassword()));
        this.updateById(user);
        log.info("用户修改密码，用户id:{},用户名:{},昵称:{}", user.getId(), user.getUserName(), user.getNickName());
    }

    @Override
    public User findUserByUserName(String username) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(User::getUserName, username);
        return this.getOne(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(UserVO vo) {
        User userUpdate = this.getById(vo.getId());
        if (Objects.isNull(userUpdate)) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "用户不存在");
        }
        // 更新好友昵称和头像
        if (!"临时用户".equals(vo.getRole()) && !userUpdate.getNickName().equals(vo.getNickName()) || ObjectUtil.notEqual(userUpdate.getHeadImageThumb(), vo.getHeadImageThumb())) {
            QueryWrapper<Friend> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Friend::getFriendId, userUpdate.getId());
            List<Friend> friends = friendService.list(queryWrapper);
            for (Friend friend : friends) {
                friend.setFriendNickName(vo.getNickName());
                friend.setFriendHeadImage(vo.getHeadImageThumb());
            }
            friendService.updateBatchById(friends);
        }
        // 更新群聊中的头像
        if (!"临时用户".equals(vo.getRole()) && ObjectUtil.notEqual(userUpdate.getHeadImageThumb(), vo.getHeadImageThumb())) {
            List<GroupMember> members = groupMemberService.findByUserId(userUpdate.getId());
            for (GroupMember member : members) {
                member.setHeadImage(vo.getHeadImageThumb());
            }
            groupMemberService.updateBatchById(members);
        }
        updateUserEntity(vo, userUpdate);
        this.updateById(userUpdate);
        log.info("用户信息更新，用户:{}}", userUpdate);

        //如果是普通用户和管理员，并且有部门，则需要添加部门
        List<UserDeptVO> userDepts = vo.getUserDepts();
        if ("临时用户".equals(vo.getRole()) || CollUtil.isEmpty(userDepts)) {
            return;
        }
        for (UserDeptVO userDept : userDepts) {
            userDept.setUserName(vo.getUserName());
        }
        userDeptService.remove(Wrappers.<UserDept>lambdaQuery().eq(UserDept::getUserName, userUpdate.getUserName()).eq(UserDept::getSysId, "IM"));
        List<UserDept> depts = MapperUtils.INSTANCE.mapAsList(UserDept.class, userDepts);
        userDeptService.saveBatch(depts);
    }

    private void updateUserEntity(UserVO vo, User userUpdate) {
        // 更新用户信息
        if (StrUtil.isNotBlank(vo.getNickName())) {
            userUpdate.setNickName(vo.getNickName());
        }
        if (vo.getSex() != null) {
            userUpdate.setSex(vo.getSex());
        }
        if (StrUtil.isNotBlank(vo.getSignature())) {
            userUpdate.setSignature(vo.getSignature());
        }
        if (StrUtil.isNotBlank(vo.getHeadImage())) {
            userUpdate.setHeadImage(vo.getHeadImage());
        }
        if (StrUtil.isNotBlank(vo.getHeadImageThumb())) {
            userUpdate.setHeadImageThumb(vo.getHeadImageThumb());
        }
        if (StrUtil.isNotBlank(vo.getRole())) {
            userUpdate.setRole(vo.getRole());
        }
        if (StrUtil.isNotBlank(vo.getEmail())) {
            userUpdate.setEmail(vo.getEmail());
        }
        if (StrUtil.isNotBlank(vo.getMobile())) {
            userUpdate.setMobile(vo.getMobile());
        }
        if (StrUtil.isNotBlank(vo.getPostTitle())) {
            userUpdate.setPostTitle(vo.getPostTitle());
        }
        if (vo.getExpireDays() != null) {
            userUpdate.setExpireDays(vo.getExpireDays());
        }

        if ("临时用户".equals(vo.getRole())) {
            userUpdate.setChatObjectIds(JSONObject.toJSONString(vo.getConnectionPersons()));
        }
    }

    @Override
    public UserVO findUserById(Long id) {
        User user = this.getById(id);
        UserVO vo = BeanUtils.copyProperties(user, UserVO.class);
        vo.setOnline(imClient.isOnline(id));
        return vo;
    }

    @Override
    public List<UserVO> findUserByName(String name) {
        LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(User::getUserName, name).or().like(User::getNickName, name).last(DbUtil.getLimitOneSql(20));
        List<User> users = this.list(queryWrapper);
        List<Long> userIds = users.stream().map(User::getId).collect(Collectors.toList());
        List<Long> onlineUserIds = imClient.getOnlineUser(userIds);
        return users.stream().map(u -> {
            UserVO vo = BeanUtils.copyProperties(u, UserVO.class);
            vo.setOnline(onlineUserIds.contains(u.getId()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OnlineTerminalVO> getOnlineTerminals(String userIds) {
        List<Long> userIdList = Arrays.stream(userIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        // 查询在线的终端
        Map<Long, List<IMTerminalType>> terminalMap = imClient.getOnlineTerminal(userIdList);
        // 组装vo
        List<OnlineTerminalVO> vos = new LinkedList<>();
        terminalMap.forEach((userId, types) -> {
            List<Integer> terminals = types.stream().map(IMTerminalType::code).collect(Collectors.toList());
            vos.add(new OnlineTerminalVO(userId, terminals));
        });
        return vos;
    }

    @Override
    public LoginVO doSsoLogin(String token) {
        //判断token是否有效,是否在有效期内
        Claims claims = DecryptionJwtUtils.verifyJwt(token);
        if (claims == null) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "token无效！");
        } else {
            String loginId = claims.get("userId") + "";
            User user = this.findUserByUserName(loginId);
            if (null == user) {
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "用户不存在");
            }
            // 生成token
            UserSession session = BeanUtils.copyProperties(user, UserSession.class);
            session.setUserId(user.getId());
            session.setTerminal(IMTerminalType.WEB.code());
            String strJson = JSON.toJSONString(session);
            String accessToken = JwtUtil.sign(user.getId(), strJson, jwtProperties.getAccessTokenExpireIn(), jwtProperties.getAccessTokenSecret());
            String refreshToken = JwtUtil.sign(user.getId(), strJson, jwtProperties.getRefreshTokenExpireIn(), jwtProperties.getRefreshTokenSecret());
            LoginVO vo = new LoginVO();
            vo.setAccessToken(accessToken);
            vo.setAccessTokenExpiresIn(jwtProperties.getAccessTokenExpireIn());
            vo.setRefreshToken(refreshToken);
            vo.setRefreshTokenExpiresIn(jwtProperties.getRefreshTokenExpireIn());
            return vo;
        }
    }

    @Override
    public Result getUnreadMessageCount(String token) {
        //判断token是否有效,是否在有效期内
        Claims claims = DecryptionJwtUtils.verifyJwt(token);
        if (claims == null) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "token无效！");
        } else {
            Map<String, Long> resultMap = new HashMap<>();
            String loginId = claims.get("userId") + "";
            User user = this.findUserByUserName(loginId);
            if (null == user) {
                throw new GlobalException(ResultCode.PROGRAM_ERROR, "用户不存在");
            }
            List<Integer> status = Arrays.asList(0, 1);
            long privateMessageCount = privateMessageService.count(Wrappers.<PrivateMessage>lambdaQuery().eq(PrivateMessage::getRecvId, user.getId()).in(PrivateMessage::getStatus, status));
            resultMap.put("privateMessageCount", privateMessageCount);
            long userId = user.getId();
            long groupMessageCount = 0;
            //群消息：要看自己在不在群里面
            List<GroupMember> groupMembers = groupMemberService.list(Wrappers.lambdaQuery(GroupMember.class).eq(GroupMember::getUserId, userId).eq(GroupMember::getQuit, false));
            if (CollUtil.isEmpty(groupMembers)) {
                groupMessageCount = 0;
                resultMap.put("groupMessageCount", groupMessageCount);
                return ResultUtils.success(resultMap);
            }
            Set<Long> groupIds = groupMembers.stream().map(GroupMember::getGroupId).collect(Collectors.toSet());
            //按照群ID分组获取最大的消息ID
            QueryWrapper<GroupMessage> groupMessageQuery = Wrappers.query();
            groupMessageQuery.lambda().in(CollUtil.isNotEmpty(groupIds), GroupMessage::getGroupId, groupIds);
            groupMessageQuery.lambda().groupBy(GroupMessage::getGroupId);
            groupMessageQuery.lambda().ne(GroupMessage::getSendId, userId);
            groupMessageQuery.select("MAX(id) as ID", "group_id as groupId");
            List<GroupMessage> groupMessages = groupMessageService.list(groupMessageQuery);
            if (CollUtil.isEmpty(groupMessages)) {
                groupMessageCount = 0;
                resultMap.put("groupMessageCount", groupMessageCount);
                return ResultUtils.success(resultMap);
            }
            for (GroupMessage groupMessage : groupMessages) {
                String key = StrUtil.join(":", RedisKey.IM_GROUP_READED_POSITION, groupMessage.getGroupId());
                Object o = redisTemplate.opsForHash().get(key, userId + "");
                long readedMaxId = Objects.isNull(o) ? -1 : Long.parseLong(o.toString());
                if (readedMaxId == -1 || groupMessage.getId() == readedMaxId) {
                    continue;
                }
                groupMessageCount++;
            }
            resultMap.put("groupMessageCount", groupMessageCount);
            return ResultUtils.success(resultMap);
        }
    }

    @Override
    public Result getUserListPage(UserQueryDTO userQueryDTO) {
        //分页com.github.pagehelper
        PageHelper.startPage(userQueryDTO.getPageNum(), userQueryDTO.getPageSize());
        List<User> users = this.list(
                Wrappers.<User>lambdaQuery()
                        .eq(StrUtil.isNotBlank(userQueryDTO.getUserName()), User::getUserName, userQueryDTO.getUserName())
                        .like(StrUtil.isNotBlank(userQueryDTO.getNickName()), User::getNickName, userQueryDTO.getNickName())
                        .eq(StrUtil.isNotBlank(userQueryDTO.getRole()), User::getRole, userQueryDTO.getRole())
                        .orderByDesc(User::getCreatedTime)
        );
        PageInfo<User> pageInfo = new PageInfo<>(users);
        PageInfo<UserVO> mgTaskDtoPageInfo = PageUtil.pageInfoCopy(pageInfo, UserVO.class);
        List<UserVO> list = mgTaskDtoPageInfo.getList();
        List<String> userNames = list.stream().map(user -> user.getUserName()).collect(Collectors.toList());
        UserDeptDTO userDeptDTO = new UserDeptDTO();
        userDeptDTO.setUserNameList(userNames);
        List<UserDeptVO> userDepts = userDeptMapper.findAllDeptAndUser(userDeptDTO);
        Map<String, List<UserDeptVO>> listMap = userDepts.stream().collect(Collectors.groupingBy(UserDeptVO::getUserName, Collectors.toList()));
        List<UserDeptVO> userDeptVOS;
        for (UserVO userVO : list) {
            if (StrUtil.isNotBlank(userVO.getChatObjectIds())) {
                userVO.setConnectionPersons(JSONObject.parseArray(userVO.getChatObjectIds(), UserDeptVO.class));
            }
            userDeptVOS = listMap.get(userVO.getUserName());
            if (CollUtil.isNotEmpty(userDeptVOS)) {
                userVO.setUserDepts(userDeptVOS);
            }
        }
        return ResultUtils.success(mgTaskDtoPageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        User user = this.getById(id);
        if (null == user) {
            return;
        }
        this.removeById(id);
        userDeptService.remove(Wrappers.<UserDept>lambdaQuery().eq(UserDept::getUserName, user.getUserName()).eq(UserDept::getSysId, "IM"));
    }

    @Override
    public Result getTempUserChatList(String userName) {
        User user = this.findUserByUserName(userName);
        if (null == user || !"临时用户".equals(user.getRole())) {
            return ResultUtils.success(new ArrayList<>());
        }
        List<UserDeptVO> connectionPersons = JSONObject.parseArray(user.getChatObjectIds(), UserDeptVO.class);
        if (CollUtil.isEmpty(connectionPersons)) {
            return ResultUtils.success(new ArrayList<>());
        }

        List<UserVO> userVOList = new ArrayList<>();
        UserVO userVO;
        String regex = "(\\w+\\()?(\\w+)(\\))?";
        Pattern pattern = Pattern.compile(regex);
        for (UserDeptVO connectionPerson : connectionPersons) {
            //正则匹配 huangjie(黄杰)，提取用户名 huangjie
            Matcher matcher = pattern.matcher(connectionPerson.getUserName());
            if (matcher.find()) {
                connectionPerson.setUserName(matcher.group(2));
            } else {
                connectionPerson.setUserName(connectionPerson.getUserName());
            }
            User userByUserName = this.findUserByUserName(connectionPerson.getUserName());
            if (null == userByUserName) {
                continue;
            }
            userVO = BeanUtils.copyProperties(userByUserName, UserVO.class);
            userVO.setOnline(imClient.isOnline(userByUserName.getId()));
            userVOList.add(userVO);
        }
        return ResultUtils.success(userVOList);
    }

    @Override
    public void resetPassword(ResetPasswordDTO resetPasswordDTO) {
        UserSession session = SessionContext.getSession();
        User user = this.getById(session.getUserId());
        if (null == user) {
            throw new GlobalException(ResultCode.USER_NOT_FOUND);
        }
        if (!"管理员".equals(user.getRole())) {
            throw new GlobalException(ResultCode.PROGRAM_ERROR, "你没有权限重置密码");
        }
        User userUpdate = this.getById(resetPasswordDTO.getId());
        userUpdate.setPassword(passwordEncoder.encode(resetPasswordDTO.getNewPassword()));
        this.updateById(userUpdate);
    }
}