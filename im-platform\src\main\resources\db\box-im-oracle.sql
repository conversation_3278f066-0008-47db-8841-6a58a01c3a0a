/*
 Navicat Premium Data Transfer

 Source Server         : *************-3307
 Source Server Type    : Oracle
 Source Server Version : 19c
 Source Host           : *************:1521
 Source Schema         : box-im

 Target Server Type    : Oracle
 Target Server Version : 19c
 File Encoding         : AL32UTF8

 Date: 22/04/2025 16:36:35
*/

-- ----------------------------
-- Table structure for IM_DEPT
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_DEPT';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_DEPT (
                         DEPT_CODE VARCHAR2(16) NOT NULL,
                         DEPT_NAME VARCHAR2(64),
                         DELETED_FLAG CHAR(1) DEFAULT '0',
                         CONSTRAINT IM_DEPT_PK PRIMARY KEY (DEPT_CODE, DELETED_FLAG)
);

COMMENT ON TABLE IM_DEPT IS '部门表';
COMMENT ON COLUMN IM_DEPT.DEPT_CODE IS '部门编码';
COMMENT ON COLUMN IM_DEPT.DEPT_NAME IS '部门名称';
COMMENT ON COLUMN IM_DEPT.DELETED_FLAG IS '删除标志0：未删除，1删除';

-- ----------------------------
-- Records of IM_DEPT
-- ----------------------------
INSERT INTO IM_DEPT (DEPT_CODE, DEPT_NAME, DELETED_FLAG) VALUES ('JGSYB', '军工事业部', '0');
INSERT INTO IM_DEPT (DEPT_CODE, DEPT_NAME, DELETED_FLAG) VALUES ('YLSYB', '医疗事业部', '0');

-- ----------------------------
-- Table structure for IM_FRIEND
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_FRIEND';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_FRIEND (
                           ID NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY,
                           USER_ID NUMBER(19) NOT NULL,
                           FRIEND_ID NUMBER(19) NOT NULL,
                           FRIEND_NICK_NAME VARCHAR2(255) NOT NULL,
                           FRIEND_HEAD_IMAGE VARCHAR2(255) DEFAULT '',
                           CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                           CONSTRAINT IM_FRIEND_PK PRIMARY KEY (ID)
);

-- Create indexes for IM_FRIEND
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_USER_ID ON IM_FRIEND(USER_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_FRIEND_ID ON IM_FRIEND(FRIEND_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

COMMENT ON TABLE IM_FRIEND IS '好友';
COMMENT ON COLUMN IM_FRIEND.ID IS 'id';
COMMENT ON COLUMN IM_FRIEND.USER_ID IS '用户id';
COMMENT ON COLUMN IM_FRIEND.FRIEND_ID IS '好友id';
COMMENT ON COLUMN IM_FRIEND.FRIEND_NICK_NAME IS '好友昵称';
COMMENT ON COLUMN IM_FRIEND.FRIEND_HEAD_IMAGE IS '好友头像';
COMMENT ON COLUMN IM_FRIEND.CREATED_TIME IS '创建时间';

-- ----------------------------
-- Table structure for IM_GROUP
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_GROUP';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_GROUP (
                          ID NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY,
                          NAME VARCHAR2(255) NOT NULL,
                          OWNER_ID NUMBER(19) NOT NULL,
                          HEAD_IMAGE VARCHAR2(255) DEFAULT '',
                          HEAD_IMAGE_THUMB VARCHAR2(255) DEFAULT '',
                          NOTICE VARCHAR2(1024) DEFAULT '',
                          REMARK VARCHAR2(255) DEFAULT '',
                          DELETED NUMBER(1) DEFAULT 0,
                          CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                          GROUP_TYPE NUMBER(1) DEFAULT 1,
                          CONSTRAINT IM_GROUP_PK PRIMARY KEY (ID)
);

COMMENT ON TABLE IM_GROUP IS '群';
COMMENT ON COLUMN IM_GROUP.ID IS 'id';
COMMENT ON COLUMN IM_GROUP.NAME IS '群名字';
COMMENT ON COLUMN IM_GROUP.OWNER_ID IS '群主id';
COMMENT ON COLUMN IM_GROUP.HEAD_IMAGE IS '群头像';
COMMENT ON COLUMN IM_GROUP.HEAD_IMAGE_THUMB IS '群头像缩略图';
COMMENT ON COLUMN IM_GROUP.NOTICE IS '群公告';
COMMENT ON COLUMN IM_GROUP.REMARK IS '群备注';
COMMENT ON COLUMN IM_GROUP.DELETED IS '是否已删除';
COMMENT ON COLUMN IM_GROUP.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN IM_GROUP.GROUP_TYPE IS '群聊类型：0：默认群聊(无法删除)；1：用户创建';

-- ----------------------------
-- Records of IM_GROUP
-- ----------------------------
INSERT INTO IM_GROUP (NAME, OWNER_ID, DELETED, CREATED_TIME, GROUP_TYPE)
VALUES ('医疗事业部', 41, 0, TO_TIMESTAMP('2024-11-08 01:55:48', 'YYYY-MM-DD HH24:MI:SS'), 1);

-- ----------------------------
-- Table structure for IM_GROUP_MEMBER
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_GROUP_MEMBER';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_GROUP_MEMBER (
                                 ID NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY,
                                 GROUP_ID NUMBER(19) NOT NULL,
                                 USER_ID NUMBER(19) NOT NULL,
                                 ALIAS_NAME VARCHAR2(255) DEFAULT '',
                                 HEAD_IMAGE VARCHAR2(255) DEFAULT '',
                                 REMARK VARCHAR2(255) DEFAULT '',
                                 QUIT NUMBER(1) DEFAULT 0,
                                 QUIT_TIME TIMESTAMP,
                                 CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                 CONSTRAINT IM_GROUP_MEMBER_PK PRIMARY KEY (ID),
                                 CONSTRAINT IM_GROUP_MEMBER_UK UNIQUE (GROUP_ID, USER_ID, QUIT)
);

-- Create indexes for IM_GROUP_MEMBER
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_GROUP_ID ON IM_GROUP_MEMBER(GROUP_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_USER_ID_IGM ON IM_GROUP_MEMBER(USER_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

COMMENT ON TABLE IM_GROUP_MEMBER IS '群成员';
COMMENT ON COLUMN IM_GROUP_MEMBER.ID IS 'id';
COMMENT ON COLUMN IM_GROUP_MEMBER.GROUP_ID IS '群id';
COMMENT ON COLUMN IM_GROUP_MEMBER.USER_ID IS '用户id';
COMMENT ON COLUMN IM_GROUP_MEMBER.ALIAS_NAME IS '组内显示名称';
COMMENT ON COLUMN IM_GROUP_MEMBER.HEAD_IMAGE IS '用户头像';
COMMENT ON COLUMN IM_GROUP_MEMBER.REMARK IS '备注';
COMMENT ON COLUMN IM_GROUP_MEMBER.QUIT IS '是否已退出';
COMMENT ON COLUMN IM_GROUP_MEMBER.QUIT_TIME IS '退出时间';
COMMENT ON COLUMN IM_GROUP_MEMBER.CREATED_TIME IS '创建时间';

-- ----------------------------
-- Records of IM_GROUP_MEMBER
-- ----------------------------
INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (92, 45, '谢宇', '测试群555', 1, TO_TIMESTAMP('2024-12-05 17:26:10', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-04 10:17:59', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (92, 53, '管理员', '测试群555', 1, TO_TIMESTAMP('2024-12-05 17:26:10', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-04 10:17:59', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, HEAD_IMAGE, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (90, 1, '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '测试222', 1, TO_TIMESTAMP('2024-12-20 11:13:52', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-04 14:27:21', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (90, 53, '管理员', '测试222', 1, TO_TIMESTAMP('2024-12-20 11:13:54', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-04 14:27:21', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, HEAD_IMAGE, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (93, 51, '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '测试888', 1, TO_TIMESTAMP('2024-12-05 17:26:05', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-05 02:18:05', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, HEAD_IMAGE, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (93, 1, '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '测试888', 1, TO_TIMESTAMP('2024-12-05 17:26:05', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-05 10:18:06', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (93, 45, '谢宇', '测试888', 1, TO_TIMESTAMP('2024-12-05 17:26:05', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-05 10:18:06', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (93, 53, '管理员', '测试888', 1, TO_TIMESTAMP('2024-12-05 17:26:05', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-05 10:18:06', 'YYYY-MM-DD HH24:MI:SS'));

INSERT INTO IM_GROUP_MEMBER (GROUP_ID, USER_ID, ALIAS_NAME, HEAD_IMAGE, REMARK, QUIT, QUIT_TIME, CREATED_TIME)
VALUES (93, 42, '卫新元', 'https://***************:9100/file/box-im/image/20241108/1731031324737.jpg', '测试888', 1, TO_TIMESTAMP('2024-12-05 17:26:05', 'YYYY-MM-DD HH24:MI:SS'), TO_TIMESTAMP('2024-12-05 10:19:59', 'YYYY-MM-DD HH24:MI:SS'));

-- ----------------------------
-- Table structure for IM_GROUP_MESSAGE
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_GROUP_MESSAGE';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_GROUP_MESSAGE (
                                  ID NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY,
                                  GROUP_ID NUMBER(19) NOT NULL,
                                  SEND_ID NUMBER(19) NOT NULL,
                                  SEND_NICK_NAME VARCHAR2(255) DEFAULT '',
                                  RECV_IDS VARCHAR2(1024) DEFAULT '',
                                  CONTENT CLOB,
                                  AT_USER_IDS VARCHAR2(1024),
                                  RECEIPT NUMBER(1) DEFAULT 0,
                                  RECEIPT_OK NUMBER(1) DEFAULT 0,
                                  TYPE NUMBER(2) NOT NULL,
                                  STATUS NUMBER(1) DEFAULT 0,
                                  SEND_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                  CONSTRAINT IM_GROUP_MESSAGE_PK PRIMARY KEY (ID)
);

-- Create indexes for IM_GROUP_MESSAGE
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_GROUP_ID_GM ON IM_GROUP_MESSAGE(GROUP_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

COMMENT ON TABLE IM_GROUP_MESSAGE IS '群消息';
COMMENT ON COLUMN IM_GROUP_MESSAGE.ID IS 'id';
COMMENT ON COLUMN IM_GROUP_MESSAGE.GROUP_ID IS '群id';
COMMENT ON COLUMN IM_GROUP_MESSAGE.SEND_ID IS '发送用户id';
COMMENT ON COLUMN IM_GROUP_MESSAGE.SEND_NICK_NAME IS '发送用户昵称';
COMMENT ON COLUMN IM_GROUP_MESSAGE.RECV_IDS IS '接收用户id,逗号分隔，为空表示发给所有成员';
COMMENT ON COLUMN IM_GROUP_MESSAGE.CONTENT IS '发送内容';
COMMENT ON COLUMN IM_GROUP_MESSAGE.AT_USER_IDS IS '被@的用户id列表，逗号分隔';
COMMENT ON COLUMN IM_GROUP_MESSAGE.RECEIPT IS '是否回执消息';
COMMENT ON COLUMN IM_GROUP_MESSAGE.RECEIPT_OK IS '回执消息是否完成';
COMMENT ON COLUMN IM_GROUP_MESSAGE.TYPE IS '消息类型 0:文字 1:图片 2:文件 3:语音 4:视频 10:系统提示';
COMMENT ON COLUMN IM_GROUP_MESSAGE.STATUS IS '状态 0:未发出 1:已送达  2:撤回 3:已读 4:已接收';
COMMENT ON COLUMN IM_GROUP_MESSAGE.SEND_TIME IS '发送时间';

-- ----------------------------
-- Table structure for IM_PRIVATE_MESSAGE
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_PRIVATE_MESSAGE';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_PRIVATE_MESSAGE (
                                    ID NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY,
                                    SEND_ID NUMBER(19) NOT NULL,
                                    RECV_ID NUMBER(19) NOT NULL,
                                    CONTENT CLOB,
                                    TYPE NUMBER(1) NOT NULL,
                                    STATUS NUMBER(1) DEFAULT 0,
                                    SEND_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    CONSTRAINT IM_PRIVATE_MESSAGE_PK PRIMARY KEY (ID)
);

-- Create indexes for IM_PRIVATE_MESSAGE
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_SEND_ID ON IM_PRIVATE_MESSAGE(SEND_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_RECV_ID ON IM_PRIVATE_MESSAGE(RECV_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

COMMENT ON TABLE IM_PRIVATE_MESSAGE IS '私聊消息';
COMMENT ON COLUMN IM_PRIVATE_MESSAGE.ID IS 'id';
COMMENT ON COLUMN IM_PRIVATE_MESSAGE.SEND_ID IS '发送用户id';
COMMENT ON COLUMN IM_PRIVATE_MESSAGE.RECV_ID IS '接收用户id';
COMMENT ON COLUMN IM_PRIVATE_MESSAGE.CONTENT IS '发送内容';
COMMENT ON COLUMN IM_PRIVATE_MESSAGE.TYPE IS '消息类型 0:文字 1:图片 2:文件 3:语音 4:视频';
COMMENT ON COLUMN IM_PRIVATE_MESSAGE.STATUS IS '状态 0:未发出 1:已送达 2:撤回 3:已读 4:已接收';
COMMENT ON COLUMN IM_PRIVATE_MESSAGE.SEND_TIME IS '发送时间';

-- ----------------------------
-- Table structure for IM_USER
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_USER';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_USER (
                         ID NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY,
                         USER_NAME VARCHAR2(255) NOT NULL,
                         NICK_NAME VARCHAR2(255) NOT NULL,
                         HEAD_IMAGE VARCHAR2(255) DEFAULT '',
                         HEAD_IMAGE_THUMB VARCHAR2(255) DEFAULT '',
                         PASSWORD VARCHAR2(255) NOT NULL,
                         MOBILE VARCHAR2(11) DEFAULT NULL,
                         EMAIL VARCHAR2(100) DEFAULT NULL,
                         SEX NUMBER(1) DEFAULT 0,
                         TYPE NUMBER(5) DEFAULT 1,
                         SIGNATURE VARCHAR2(1024) DEFAULT '',
                         LAST_LOGIN_TIME TIMESTAMP DEFAULT NULL,
                         CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                         POST_TITLE VARCHAR2(100) DEFAULT NULL,
                         EXPIRE_DAYS NUMBER(10) DEFAULT NULL,
                         ROLE VARCHAR2(100) DEFAULT '普通用户' NOT NULL,
                         CHAT_OBJECT_IDS VARCHAR2(4000) DEFAULT NULL,
                         CONSTRAINT IM_USER_PK PRIMARY KEY (ID)
);

-- Create indexes for IM_USER
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX IDX_USER_NAME ON IM_USER(USER_NAME)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IDX_NICK_NAME ON IM_USER(NICK_NAME)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

COMMENT ON TABLE IM_USER IS '用户';
COMMENT ON COLUMN IM_USER.ID IS 'id';
COMMENT ON COLUMN IM_USER.USER_NAME IS '用户名';
COMMENT ON COLUMN IM_USER.NICK_NAME IS '用户昵称';
COMMENT ON COLUMN IM_USER.HEAD_IMAGE IS '用户头像';
COMMENT ON COLUMN IM_USER.HEAD_IMAGE_THUMB IS '用户头像缩略图';
COMMENT ON COLUMN IM_USER.PASSWORD IS '密码(明文)';
COMMENT ON COLUMN IM_USER.MOBILE IS '手机号';
COMMENT ON COLUMN IM_USER.EMAIL IS '邮箱';
COMMENT ON COLUMN IM_USER.SEX IS '性别 0:男 1:女';
COMMENT ON COLUMN IM_USER.TYPE IS '用户类型 1:普通用户 2:审核账户';
COMMENT ON COLUMN IM_USER.SIGNATURE IS '个性签名';
COMMENT ON COLUMN IM_USER.LAST_LOGIN_TIME IS '最后登录时间';
COMMENT ON COLUMN IM_USER.CREATED_TIME IS '创建时间';
COMMENT ON COLUMN IM_USER.POST_TITLE IS '岗位职称';
COMMENT ON COLUMN IM_USER.EXPIRE_DAYS IS '过期天数';
COMMENT ON COLUMN IM_USER.ROLE IS '用户角色，固定三种角色：管理员、普通用户、临时用户';
COMMENT ON COLUMN IM_USER.CHAT_OBJECT_IDS IS '临时用户聊天对象Id，多个对象用逗号隔开';

-- ----------------------------
-- Records of IM_USER
-- ----------------------------
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('1', 'huangjie', '黄杰', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', 'https://***************:9100/file/box-im/image/20241104/1730700374223.png', '$2a$10$BXEZqIS2l6PuSYeD6QhDnOWTtvh7OyNbKUt5QQRCmRJJ9rUApeSry', '0', '1', '哈哈哈哈哈', NULL, TO_DATE('2024-05-13 01:51:01', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('30', 'chen_jun', '陈俊', NULL, NULL, '$2a$10$kIVe.De2hJiAkWw3ilIqOOBmJpFCiwolnAMeo4wzqI/373kLSZ/Cq', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:44:46', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('31', 'deng_xiaoling', '邓小玲', NULL, NULL, '$2a$10$33/GGnsyJ9bPjbs4J/sweuYlwC5g3MSFpF3mrIU2cALXC2kuYb6ji', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, 'BOSS', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('32', 'dou_yi', '豆易', NULL, NULL, '$2a$10$gWWnOpRDDnhipwMUf5nJzugBVqnmZxNouYypbLBpdC0lqjr2CiECW', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:48:35', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '项目经理', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('33', 'he_ping', '何平', 'https://***************:9100/file/box-im/image/20241127/1732695558532.jpg', 'https://***************:9100/file/box-im/image/20241127/1732695558686.jpg', '$2a$10$BRLVgv67PZP20/Lh0GHSAeiKEiWBFdZ6Fq/9Rry5MCRZQ4tzi/LFe', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:50:09', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('34', 'hu_jingling', '胡晶玲', NULL, NULL, '$2a$10$ktJrY0wU.TeoM6ahF47kJ.tCA7AVK08ksoORDjmdsqsdOiE52kJKO', '1', '1', NULL, NULL, TO_DATE('2024-11-01 08:50:36', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('35', 'jiang_tengde', '姜腾德', NULL, NULL, '$2a$10$wA/jnasmGOUVc99e/ztTTes/yx.2KViWZJvdDJq2Z/a/K/H0Lk6Pu', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:51:11', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('36', 'li_jiankui', '李建奎', NULL, NULL, '$2a$10$l44op8mp8hnVzQKroI28RuMxfT9ibgMsaVAIjHrmkQtE69HZOG99O', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:51:40', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('37', 'li_ruiling', '李蕊伶', 'https://***************:9100/file/box-im/image/20241108/1731031402211.png', 'https://***************:9100/file/box-im/image/20241108/1731031402633.png', '$2a$10$zDpRBaEIZxlhVm50.Z.d..gASaZGqPIcLPn2bGNbIMN3hc9GMmES6', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:52:01', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('38', 'li_xin', '李欣', 'https://***************:9100/file/box-im/image/20241108/1731031322654.png', 'https://***************:9100/file/box-im/image/20241108/1731031323138.png', '$2a$10$vnf4.BmZwQbolyO4egI7beIw4ccGFKDeIXeU.tGf0aOwZV.KU1vS2', '1', '1', '牛，马累了会休息；牛马累了只会给自己点两杯咖啡', NULL, TO_DATE('2024-11-01 08:52:34', 'SYYYY-MM-DD HH24:MI:SS'), '18408260038', 'BOSS', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('39', 'sun_yang', '孙杨', NULL, NULL, '$2a$10$LEbmQ8QRGWBxYcl7LXiZ1ueS/DkmgcGgWlMhZhOuCUcNawpa6ZIva', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:53:01', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('40', 'tan_jun', '谭军', NULL, NULL, '$2a$10$BcSVewiUgwgzUIVgeJa.JeX/WPe7k9c12JJdHGCa2A2auYvRHh6Q2', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:53:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '后端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('41', 'wang_xing', '王星', NULL, NULL, '$2a$10$IU0/Hbjci4CJumLH3V9X0u16sDolUCt/eimCZttoBSjDaSJcRBL.a', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:53:53', 'SYYYY-MM-DD HH24:MI:SS'), NULL, 'BOSS', '<EMAIL>', NULL, '管理员', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('42', 'wei_xinyuan', '卫新元', 'https://***************:9100/file/box-im/image/20241108/1731031324596.jpg', 'https://***************:9100/file/box-im/image/20241108/1731031324737.jpg', '$2a$10$GcipCQk82trB/Fj8EpLP4uzCsGcgguJLuoK1UH2aKxG2IbACMr7Py', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:54:13', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('43', 'wu_fengyang', '伍枫飏', NULL, NULL, '$2a$10$xaOnSFuNp/di0ccKuSAo3emsc.9bekh5plviG3K5U4r0xYFEw/oba', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:54:33', 'SYYYY-MM-DD HH24:MI:SS'), NULL, 'UI', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('44', 'xiao_fu', '骁釜', NULL, NULL, '$2a$10$IQLIE07i8Yo2vciwZNLQDeEko.p6lhLIhV1h07HeM1xGu1qVNEpMW', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:55:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '大数据', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('45', 'xie_yu', '谢宇', NULL, NULL, '$2a$10$RHbMhxa0Amwn02hc6U5SUOOR/O8NjS7EaJkbBrmGkN8m4zuH3a6Q.', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:55:54', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('46', 'xu_miao', '徐苗', NULL, NULL, '$2a$10$AujQ0J5N3Elr/T7OpSpAb.uyTWvemIClJm.96b1b29gDRdjynfn5a', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:56:14', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '	<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('47', 'xu_shan', '胥珊', 'https://***************:9100/file/box-im/image/20241203/1733212714853.png', 'https://***************:9100/file/box-im/image/20241203/1733212715427.png', '$2a$10$5/.7j.VZT1/.4rxrUjLGwu8UNbPCNMzoPeToswghqX7ilxZxcidQ2', '0', '1', 'ddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd', NULL, TO_DATE('2024-11-01 08:56:43', 'SYYYY-MM-DD HH24:MI:SS'), '15245454545', '运维', '	<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('48', 'yang_jiong', '杨迥', NULL, NULL, '$2a$10$3as.39boEdzMOvutNdgKXu67H30kAijeNtJOj.f3L4CULCbkKUEwW', '0', '1', '黑暗笼罩万物，我将是黑暗中最后的那道曙光，以雷霆，击碎黑暗！', NULL, TO_DATE('2024-11-01 08:57:10', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '运维', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('50', 'zhang_daojian', '张道见', NULL, NULL, '$2a$10$S82MyOjN3lxRFGCwpFqLGeGYtirFyp6ehwK76XgMhIOfuPcdAvq.u', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:57:55', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '项目经理', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('51', 'zhao_xing', '赵星', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', 'https://***************:9100/file/box-im/image/20241113/1731477745354.jpg', '$2a$10$vsp91MUYpPklYRhCn5ubl.4Dd8BdosQupXjMcnHW3KzzM1/b4Ses2', '0', '1', 'haahah', NULL, TO_DATE('2024-11-01 08:58:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '前端开发', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('52', 'zou_yunchuan', '邹云川', NULL, NULL, '$2a$10$3E/lo89nv9fIwBqzPIF2feweh0za3M8A4C6TDJJhYkRjK3kfgltLe', '0', '1', NULL, NULL, TO_DATE('2024-11-01 08:58:44', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '产品经理', '<EMAIL>', NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('53', 'admin', '管理员', NULL, NULL, '$2a$10$jZpJjA2QuCa0VTOGicbIO.96eMNC./VwZGVTMyGY8mIgF21RPKo3u', '0', '1', NULL, NULL, TO_DATE('2024-11-01 09:04:35', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '管理员', NULL, NULL, '管理员', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('54', 'shen_xuchao', '沈序超', NULL, NULL, '$2a$10$I2CG4ZeXcj7dtBZEQSgLU.7px3i0DmBhmEI7KIpJUSoJrmQ4U.jKC', '0', '1', NULL, NULL, TO_DATE('2024-11-01 09:14:03', 'SYYYY-MM-DD HH24:MI:SS'), NULL, '售前', NULL, NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('55', 'ceshi', '临时用户', NULL, NULL, '$2a$10$fRB9mywstHgrPy.w1UBcDO5csr6n422LAqqHx7HtpdG0JN2mcWl4K', '0', '1', NULL, NULL, TO_DATE('2024-11-21 03:51:09', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, '30', '临时用户', '[{"deptCode":"YLSYB","deptName":"医疗事业部","groupName":"佳缘科技","nickName":"谢宇","organizationName":"成都","userId":45,"userName":"xie_yu"}]');
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('56', 'li_rui', '李锐', NULL, NULL, '$2a$10$ktJrY0wU.TeoM6ahF47kJ.tCA7AVK08ksoORDjmdsqsdOiE52kJKO', '0', '1', NULL, NULL, TO_DATE('2024-12-03 09:32:09', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('57', 'yu_chenghao', '宇成浩', NULL, NULL, '$2a$10$I3ndU.UdNYlH8wUyqI/wZewRmiw5dl9bhbDPSYD6PYqmpGeBrgTaO', '0', '1', NULL, NULL, TO_DATE('2025-02-28 02:21:58', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('58', 'zou_luncong', '邹伦聪', NULL, NULL, '$2a$10$JOykfAuPvYjOIiffvMQzWeoDU6XC1LlEPS9kLMiEPHVtmediHRz2a', '0', '1', NULL, NULL, TO_DATE('2025-02-28 02:22:45', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);
INSERT INTO "IM_USER" ("ID", "USER_NAME", "NICK_NAME", "HEAD_IMAGE", "HEAD_IMAGE_THUMB", "PASSWORD", "SEX", "TYPE", "SIGNATURE", "LAST_LOGIN_TIME", "CREATED_TIME", "MOBILE", "POST_TITLE", "EMAIL", "EXPIRE_DAYS", "ROLE", "CHAT_OBJECT_IDS") VALUES ('59', 'li_yuliang', '李余亮', NULL, NULL, '$2a$10$IZzmn.x8l3AOqJL8OwJhke/AkHZlhjYucgtKYPnymAtgNaryHZwkG', '0', '1', NULL, NULL, TO_DATE('2025-03-04 01:12:37', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL, NULL, '普通用户', NULL);


-- ----------------------------
-- Table structure for IM_USER_DEPT
-- ----------------------------
BEGIN
EXECUTE IMMEDIATE 'DROP TABLE IM_USER_DEPT';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -942 THEN
         RAISE;
END IF;
END;
/

CREATE TABLE IM_USER_DEPT (
                              GROUP_NAME VARCHAR2(100) DEFAULT '01',
                              ORGANIZATION_NAME VARCHAR2(100) DEFAULT '001',
                              USER_DEPT_ID NUMBER(10) GENERATED BY DEFAULT ON NULL AS IDENTITY,
                              USER_NAME VARCHAR2(255),
                              DEPT_CODE VARCHAR2(50),
                              SYS_ID VARCHAR2(50) DEFAULT 'IM',
                              CONSTRAINT IM_USER_DEPT_PK PRIMARY KEY (USER_DEPT_ID)
);

-- Create indexes for IM_USER_DEPT
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IM_USER_DEPT_USER_NAME_IDX ON IM_USER_DEPT(USER_NAME, SYS_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX IM_USER_DEPT_DEPT_CODE_IDX ON IM_USER_DEPT(DEPT_CODE, SYS_ID)';
EXCEPTION
   WHEN OTHERS THEN
      IF SQLCODE != -955 THEN
         RAISE;
END IF;
END;
/

COMMENT ON TABLE IM_USER_DEPT IS '用户部门关系表';
COMMENT ON COLUMN IM_USER_DEPT.GROUP_NAME IS '集团名称';
COMMENT ON COLUMN IM_USER_DEPT.ORGANIZATION_NAME IS '组织名称';
COMMENT ON COLUMN IM_USER_DEPT.USER_DEPT_ID IS '用户科室id';
COMMENT ON COLUMN IM_USER_DEPT.USER_NAME IS '用户名称';
COMMENT ON COLUMN IM_USER_DEPT.DEPT_CODE IS '科室编码';
COMMENT ON COLUMN IM_USER_DEPT.SYS_ID IS '系统编码';

-- ----------------------------
-- Records of IM_USER_DEPT
-- ----------------------------
INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('huangjie', 'JGSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('admin', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('xie_yu', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('zhao_xing', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('wei_xinyuan', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('chen_jun', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('deng_xiaoling', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('dou_yi', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('he_ping', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('hu_jingling', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('jiang_tengde', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('li_jiankui', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('li_ruiling', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('li_xin', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('sun_yang', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('tan_jun', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('wang_xing', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('wu_fengyang', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('xiao_fu', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('xu_miao', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('xu_shan', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('yang_jiong', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('zhang_daojian', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('zou_yunchuan', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('shen_xuchao', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('ceshi', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('li_rui', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('yu_chenghao', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('zou_luncong', 'YLSYB', '佳缘科技', '成都', 'IM');

INSERT INTO IM_USER_DEPT (USER_NAME, DEPT_CODE, GROUP_NAME, ORGANIZATION_NAME, SYS_ID)
VALUES ('li_yuliang', 'YLSYB', '佳缘科技', '成都', 'IM');
