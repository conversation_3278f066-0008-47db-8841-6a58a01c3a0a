package com.bx.implatform.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户部门关系表
 *
 * @TableName im_user_dept
 */
@TableName(value = "im_user_dept")
@Data
public class UserDept implements Serializable {
    /**
     * 用户科室id
     */
    @ApiModelProperty(value = "用户科室id")
    @TableId(value = "user_dept_id", type = IdType.AUTO)
    private Integer userDeptId;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    @TableField("group_name")
    private String groupName;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    @TableField("organization_name")
    private String organizationName;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField("user_name")
    private String userName;

    /**
     * 科室编码
     */
    @ApiModelProperty(value = "科室编码")
    @TableField("dept_code")
    private String deptCode;

    /**
     * 系统编码
     */
    @ApiModelProperty(value = "系统编码")
    @TableField("sys_id")
    private String sysId = "IM";

}
