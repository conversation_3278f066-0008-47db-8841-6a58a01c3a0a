package com.bx.implatform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bx.imclient.IMClient;
import com.bx.implatform.dto.UserDeptDTO;
import com.bx.implatform.entity.DBTreeNode;
import com.bx.implatform.entity.UserDept;
import com.bx.implatform.mapper.UserDeptMapper;
import com.bx.implatform.result.Result;
import com.bx.implatform.result.ResultUtils;
import com.bx.implatform.service.UserDeptService;
import com.bx.implatform.vo.UserDeptVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【im_user_dept(用户部门关系表)】的数据库操作Service实现
 * @createDate 2024-05-21 15:19:27
 */
@Service
public class UserDeptServiceImpl extends ServiceImpl<UserDeptMapper, UserDept>
        implements UserDeptService {
    @Autowired
    private UserDeptMapper userDeptMapper;

    @Autowired
    private IMClient imClient;

    /**
     * 获取组织结构（院区-科室-用户）
     *
     * @param userDeptDTO
     * @return
     */
    @Override
    public Result getOrganizationalStructure(UserDeptDTO userDeptDTO) {
        //获取院区-科室-用户
        List<UserDeptVO> mapList = userDeptMapper.findAllDeptAndUser(userDeptDTO);
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, List<UserDeptVO>> groupMap = mapList.stream().collect(Collectors.groupingBy(item -> item.getGroupName() + "_" + item.getOrganizationName()));
        for (Map.Entry<String, List<UserDeptVO>> stringListEntry : groupMap.entrySet()) {
            String groupCode = stringListEntry.getKey();
            List<UserDeptVO> entryValue = stringListEntry.getValue();
            //部门树
            List<Tree<Integer>> treeNodes = getTreeNodes(entryValue);
            resultMap.put(groupCode, treeNodes);
        }
        return ResultUtils.success(resultMap);
    }

    @Override
    public Result getOnlyOrganizationalStructure(UserDeptDTO userDeptDTO) {
        //获取院区-科室
        List<UserDeptVO> mapList = userDeptMapper.findAllDept(userDeptDTO);
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, List<UserDeptVO>> groupMap = mapList.stream().collect(Collectors.groupingBy(item -> item.getGroupName() + "_" + item.getOrganizationName()));
        for (Map.Entry<String, List<UserDeptVO>> stringListEntry : groupMap.entrySet()) {
            String groupCode = stringListEntry.getKey();
            List<UserDeptVO> entryValue = stringListEntry.getValue();
            //部门树
            List<Tree<Integer>> treeNodes = getTreeNodesNoUser(entryValue);
            resultMap.put(groupCode, treeNodes);
        }
        return ResultUtils.success(resultMap);
    }

    private List<Tree<Integer>> getTreeNodes(List<UserDeptVO> entryValue) {
        Map<String, List<UserDeptVO>> listMap = entryValue.stream().collect(Collectors.groupingBy(UserDeptVO::getDeptName));
        List<DBTreeNode> addList = new ArrayList<>();
        int i = 0;
        Map mapDept;
        Map<String, Object> map;
        List<Long> onlineUserIds;
        for (Map.Entry<String, List<UserDeptVO>> entity : listMap.entrySet()) {
            int deptNum = ++i;
            String key = entity.getKey();
            mapDept = new HashMap();
            if (CollUtil.isNotEmpty(entity.getValue())) {
                mapDept.put("deptCode", entity.getValue().get(0).getDeptCode());
            }

            mapDept.put("deptName", key);
            addTreeList(addList, deptNum, 0, key, mapDept);
            List<UserDeptVO> value = entity.getValue();
            onlineUserIds = imClient.getOnlineUser(entryValue.stream().map(UserDeptVO::getUserId).collect(Collectors.toList()));
            for (UserDeptVO userDeptVO : value) {
                map = BeanUtil.beanToMap(userDeptVO, "deptCode", "deptName", "userName", "nickName", "userId", "postTitle", "sex", "headImage", "email", "mobile");
                if (onlineUserIds.contains(userDeptVO.getUserId())) {
                    map.put("online", true);
                } else {
                    map.put("online", false);
                }
                userDeptVO.setExtMap(map);
            }
            //value中getExtMap().get("online")为true的排在前面
            value.sort((o1, o2) -> {
                if ((boolean) o1.getExtMap().get("online") && !(boolean) o2.getExtMap().get("online")) {
                    return -1;
                } else if (!(boolean) o1.getExtMap().get("online") && (boolean) o2.getExtMap().get("online")) {
                    return 1;
                } else {
                    return 0;
                }
            });
            for (UserDeptVO userDeptVO : value) {
                int userNum = ++i;
                addTreeList(addList, userNum, deptNum, userDeptVO.getUserName() + "(" + userDeptVO.getNickName() + ")", userDeptVO.getExtMap());
            }
        }
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("pid");
        treeNodeConfig.setNameKey("label");

        List<Tree<Integer>> treeNodes = TreeUtil.build(addList, 0, treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getPid());
                    tree.setWeight(treeNode.getId());
                    tree.setName(treeNode.getName());
                    tree.putExtra("userInfo", treeNode.getExtra());
                });
        return treeNodes;
    }

    private void addTreeList(List<DBTreeNode> addList, int id, int pid, String name, Map<String, Object> map) {
        DBTreeNode dbTreeNode = new DBTreeNode();
        dbTreeNode.setId(id);
        dbTreeNode.setName(name);
        dbTreeNode.setPid(pid);
        dbTreeNode.setExtra(map);
        addList.add(dbTreeNode);
    }


    private List<Tree<Integer>> getTreeNodesNoUser(List<UserDeptVO> entryValue) {
        Map<String, List<UserDeptVO>> listMap = entryValue.stream().collect(Collectors.groupingBy(UserDeptVO::getDeptName));
        List<DBTreeNode> addList = new ArrayList<>();
        int i = 0;
        Map mapDept;
        Map<String, Object> map;
        List<Long> onlineUserIds;
        for (Map.Entry<String, List<UserDeptVO>> entity : listMap.entrySet()) {
            int deptNum = ++i;
            String key = entity.getKey();
            mapDept = new HashMap();
            if (CollUtil.isNotEmpty(entity.getValue())) {
                mapDept.put("deptCode", entity.getValue().get(0).getDeptCode());
            }

            mapDept.put("deptName", key);
            addTreeList(addList, deptNum, 0, key, mapDept);
        }
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setParentIdKey("pid");
        treeNodeConfig.setNameKey("label");

        List<Tree<Integer>> treeNodes = TreeUtil.build(addList, 0, treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getPid());
                    tree.setWeight(treeNode.getId());
                    tree.setName(treeNode.getName());
                    tree.putExtra("userInfo", treeNode.getExtra());
                });
        return treeNodes;
    }
}
